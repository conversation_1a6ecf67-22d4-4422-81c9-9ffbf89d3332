<?php
$page_title = "My Certificate";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

$conn = getDbConnection();

// Check if certificate ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['message'] = 'Invalid certificate ID';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/certificates.php');
}

$certificate_id = (int) $_GET['id'];

// Check if certificate exists and belongs to this student
$cert_query = "SELECT c.* FROM certificates c WHERE c.id = ? AND c.student_id = ?";
$stmt = mysqli_prepare($conn, $cert_query);
mysqli_stmt_bind_param($stmt, "ii", $certificate_id, $student['id']);
mysqli_stmt_execute($stmt);
$cert_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($cert_result) == 0) {
    $_SESSION['message'] = 'Certificate not found or does not belong to you';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/certificates.php');
}

// Get certificate details with all required information
$sql = "SELECT c.*, s.full_name, s.father_name, s.cnic, s.profile_photo, co.course_name, co.duration
        FROM certificates c
        JOIN students s ON c.student_id = s.id
        JOIN courses co ON c.course_id = co.id
        WHERE c.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $certificate_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = 'Certificate not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

$certificate = mysqli_fetch_assoc($result);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate - <?php echo $certificate['full_name']; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Playfair Display', serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            /* A4 size optimization */
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }

        .certificate-container {
            width: 100%;
            margin: 0 auto;
            position: relative;
            /* Ensure content fits on A4 */
            box-sizing: border-box;
        }

        .certificate {
            background-color: white;
            padding: 30px;
            border: 15px solid transparent;
            border-image: linear-gradient(45deg, #4361ee, #7209b7) 1;
            position: relative;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
            /* Ensure content fits on A4 */
            box-sizing: border-box;
            height: 277mm;
        }

        .certificate::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('<?php echo SITE_URL; ?>assets/images/logo-watermark.png');
            background-position: center;
            background-repeat: no-repeat;
            background-size: 50%;
            opacity: 0.05;
            pointer-events: none;
            z-index: 0;
        }

        .certificate-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .certificate-title {
            font-size: 42px;
            font-weight: bold;
            color: #4361ee;
            margin-bottom: 10px;
            font-family: 'Dancing Script', cursive;
        }

        .certificate-subtitle {
            font-size: 22px;
            color: #7209b7;
            margin-bottom: 0;
        }

        .certificate-content {
            text-align: center;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .student-photo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .student-name {
            font-size: 28px;
            font-weight: bold;
            color: #4361ee;
            margin: 15px 0;
        }

        .course-name {
            font-size: 20px;
            color: #7209b7;
            margin: 10px 0;
        }

        .certificate-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            position: relative;
            z-index: 1;
        }

        .signature {
            text-align: center;
            width: 180px;
        }

        .signature-line {
            border-top: 2px solid #4361ee;
            margin-bottom: 8px;
        }

        .certificate-number {
            position: absolute;
            bottom: 80px;
            right: 30px;
            font-size: 13px;
            color: #6c757d;
        }

        .certificate-date {
            position: absolute;
            bottom: 80px;
            left: 30px;
            font-size: 13px;
            color: #6c757d;
        }

        .verification {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            width: 100%;
            z-index: 1;
        }

        .qr-code {
            width: 100px;
            height: 100px;
            margin: 0 auto 5px;
            display: block;
        }

        .verification p {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
            font-family: 'Montserrat', sans-serif;
        }

        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(90deg, #4361ee, #7209b7);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.5px;
            z-index: 100;
        }

        .print-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
            color: white;
        }

        .back-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.5px;
            z-index: 100;
            text-decoration: none;
        }

        .back-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            color: white;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 0;
                width: 210mm;
                height: 297mm;
            }

            .certificate-container {
                width: 100%;
                height: 100%;
            }

            .certificate {
                box-shadow: none;
                border-width: 10px;
                padding: 20px;
                height: 277mm;
                page-break-inside: avoid;
            }

            .print-button, .back-button {
                display: none;
            }

            /* Ensure content fits on A4 */
            .certificate-title {
                font-size: 38px;
            }

            .certificate-subtitle {
                font-size: 20px;
            }

            .certificate-content {
                font-size: 14px;
            }

            .student-name {
                font-size: 26px;
            }

            .course-name {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>

    <div class="certificate-container">
        <div class="certificate">
            <div class="certificate-header">
                <h1 class="certificate-title">Certificate of Completion</h1>
                <p class="certificate-subtitle"><?php echo SITE_NAME; ?></p>
            </div>

            <div class="certificate-content">
                <?php if (!empty($certificate['profile_photo'])): ?>
                    <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $certificate['profile_photo']; ?>" alt="Student Photo" class="student-photo" onerror="this.src='<?php echo SITE_URL; ?>assets/images/default-profile.png'">
                <?php else: ?>
                    <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="student-photo">
                <?php endif; ?>

                <p>This is to certify that</p>
                <h2 class="student-name"><?php echo $certificate['full_name']; ?></h2>
                <p>son/daughter of <strong><?php echo $certificate['father_name']; ?></strong> with CNIC <strong><?php echo $certificate['cnic']; ?></strong> has successfully completed the course of</p>
                <h3 class="course-name"><?php echo $certificate['course_name']; ?> (<?php echo $certificate['duration']; ?>)</h3>
                <p>with satisfactory performance and dedication.</p>
            </div>

            <div class="certificate-footer">
                <div class="signature">
                    <div class="signature-line"></div>
                    <p>Principal</p>
                    <p><?php echo SITE_NAME; ?></p>
                </div>

                <div class="signature">
                    <div class="signature-line"></div>
                    <p>Director</p>
                    <p><?php echo SITE_NAME; ?></p>
                </div>
            </div>

            <div class="certificate-date">
                Issue Date: <?php echo formatDate($certificate['issue_date']); ?>
            </div>

            <div class="certificate-number">
                Certificate No: <?php echo $certificate['certificate_number']; ?>
            </div>

            <div class="verification">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=<?php echo urlencode(SITE_URL . 'verify.php?cert=' . $certificate['certificate_number']); ?>&color=4361ee" alt="Scan to verify certificate" class="qr-code">
                <p>Scan QR code to verify certificate</p>
            </div>
        </div>
    </div>

    <button class="print-button" onclick="window.print()">
        <i class="fas fa-print"></i> Print Certificate
    </button>

    <a href="<?php echo SITE_URL; ?>student/certificates.php" class="back-button">
        <i class="fas fa-arrow-left"></i> Back to Certificates
    </a>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
