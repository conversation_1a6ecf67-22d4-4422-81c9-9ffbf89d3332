# Disable directory browsing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Protect config files
<FilesMatch "^(config\.php|db\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Redirect to error page for non-existent files
ErrorDocument 404 /cti/index.php

# PHP error handling
php_flag display_errors off
php_value error_reporting E_ALL

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
</IfModule>
