<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

// Check if certificate ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['message'] = 'Certificate ID is required';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

$certificate_id = (int) $_GET['id'];
$conn = getDbConnection();

// Verify that the certificate belongs to the logged-in student
$verify_query = "SELECT c.* FROM certificates c WHERE c.id = ? AND c.student_id = ?";
$stmt = mysqli_prepare($conn, $verify_query);
mysqli_stmt_bind_param($stmt, "ii", $certificate_id, $student['id']);
mysqli_stmt_execute($stmt);
$verify_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($verify_result) == 0) {
    $_SESSION['message'] = 'You do not have permission to access this certificate';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

// Get certificate details
$sql = "SELECT c.*, s.full_name, s.father_name, s.cnic, s.profile_photo, co.course_name, co.duration
        FROM certificates c
        JOIN students s ON c.student_id = s.id
        JOIN courses co ON c.course_id = co.id
        WHERE c.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $certificate_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = 'Certificate not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/dashboard.php');
}

$certificate = mysqli_fetch_assoc($result);

// Check if mPDF is available
$mpdf_available = class_exists('\Mpdf\Mpdf');

if (!$mpdf_available) {
    $_SESSION['message'] = 'PDF generation is not available. Please contact administrator.';
    $_SESSION['message_type'] = 'warning';
    redirect(SITE_URL . 'student/certificate.php?id=' . $certificate_id);
}

// Generate certificate HTML
$html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate - ' . $certificate['full_name'] . '</title>
    <style>
        body {
            font-family: "Helvetica", "Arial", sans-serif;
            margin: 0;
            padding: 0;
            background-color: white;
        }
        
        .certificate-container {
            width: 100%;
            margin: 0 auto;
        }
        
        .certificate {
            background-color: white;
            border: 15px solid #4361ee;
            border-image: linear-gradient(45deg, #4361ee, #7209b7) 1;
            padding: 40px;
            position: relative;
        }
        
        .certificate-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .certificate-title {
            font-size: 42px;
            font-weight: bold;
            color: #4361ee;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .certificate-subtitle {
            font-size: 22px;
            font-weight: 600;
            color: #7209b7;
            margin-bottom: 0;
        }
        
        .certificate-content {
            text-align: center;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .student-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 0 auto 15px;
            display: block;
        }
        
        .student-name {
            font-size: 28px;
            font-weight: bold;
            color: #4361ee;
            margin: 15px 0;
        }
        
        .course-name {
            font-size: 20px;
            color: #7209b7;
            margin: 10px 0;
        }
        
        .certificate-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        
        .signature {
            text-align: center;
            width: 180px;
        }
        
        .signature-line {
            border-top: 2px solid #4361ee;
            margin-bottom: 8px;
        }
        
        .certificate-number {
            position: absolute;
            bottom: 80px;
            right: 30px;
            font-size: 13px;
            color: #6c757d;
        }
        
        .certificate-date {
            position: absolute;
            bottom: 80px;
            left: 30px;
            font-size: 13px;
            color: #6c757d;
        }
        
        .verification {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            width: 100%;
        }
        
        .qr-code {
            width: 100px;
            height: 100px;
            margin: 0 auto 5px;
            display: block;
        }
        
        .verification p {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate">
            <div class="certificate-header">
                <h1 class="certificate-title">Certificate of Completion</h1>
                <p class="certificate-subtitle">' . SITE_NAME . '</p>
            </div>
            
            <div class="certificate-content">
                ' . (!empty($certificate['profile_photo']) ? 
                    '<img src="' . SITE_URL . 'assets/uploads/profile_photos/' . $certificate['profile_photo'] . '" alt="Student Photo" class="student-photo">' : 
                    '<img src="' . SITE_URL . 'assets/images/default-profile.png" alt="Default Profile" class="student-photo">') . '
                
                <p>This is to certify that</p>
                <h2 class="student-name">' . $certificate['full_name'] . '</h2>
                <p>son/daughter of <strong>' . $certificate['father_name'] . '</strong> with CNIC <strong>' . $certificate['cnic'] . '</strong> has successfully completed the course of</p>
                <h3 class="course-name">' . $certificate['course_name'] . ' (' . $certificate['duration'] . ')</h3>
                <p>with satisfactory performance and dedication.</p>
            </div>
            
            <div class="certificate-footer">
                <div class="signature">
                    <div class="signature-line"></div>
                    <p>Principal</p>
                    <p>' . SITE_NAME . '</p>
                </div>
                
                <div class="signature">
                    <div class="signature-line"></div>
                    <p>Director</p>
                    <p>' . SITE_NAME . '</p>
                </div>
            </div>
            
            <div class="certificate-date">
                Issue Date: ' . date('d-m-Y', strtotime($certificate['issue_date'])) . '
            </div>
            
            <div class="certificate-number">
                Certificate No: ' . $certificate['certificate_number'] . '
            </div>
            
            <div class="verification">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=' . urlencode(SITE_URL . 'verify.php?cert=' . $certificate['certificate_number']) . '&color=4361ee" alt="Scan to verify certificate" class="qr-code">
                <p>Scan QR code to verify certificate</p>
            </div>
        </div>
    </div>
</body>
</html>';

// Create mPDF instance
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4',
    'margin_left' => 10,
    'margin_right' => 10,
    'margin_top' => 10,
    'margin_bottom' => 10
]);

// Set document metadata
$mpdf->SetTitle('Certificate - ' . $certificate['certificate_number']);
$mpdf->SetAuthor(SITE_NAME);
$mpdf->SetCreator(SITE_NAME);

// Write HTML to PDF
$mpdf->WriteHTML($html);

// Output PDF for download
$mpdf->Output('Certificate_' . $certificate['certificate_number'] . '.pdf', 'D');
exit;
?>
