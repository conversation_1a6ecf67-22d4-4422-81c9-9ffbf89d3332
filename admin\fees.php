<?php
$page_title = "Manage Fees";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Process fee actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' || isset($_GET['action'])) {
    // Mark fee as paid
    if (isset($_POST['mark_paid']) || (isset($_GET['action']) && $_GET['action'] == 'mark_paid')) {
        $fee_id = 0;
        $student_id = 0;

        if (isset($_POST['fee_id'])) {
            $fee_id = (int) $_POST['fee_id'];
            $student_id = (int) $_POST['student_id'];
        } elseif (isset($_GET['fee_id'])) {
            $fee_id = (int) $_GET['fee_id'];
            $student_id = (int) $_GET['student_id'];
        }

        $sql = "UPDATE fees SET status = 'paid', payment_date = CURDATE() WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $fee_id);

        if (mysqli_stmt_execute($stmt)) {
            // Check if course is completed to generate certificate
            $student_result = mysqli_query($conn, "SELECT course_status, course_id FROM students WHERE id = $student_id");
            $student = mysqli_fetch_assoc($student_result);

            if ($student['course_status'] == 'completed') {
                // Generate certificate if not already exists
                $cert_check = mysqli_query($conn, "SELECT id FROM certificates WHERE student_id = $student_id");

                if (mysqli_num_rows($cert_check) == 0) {
                    // Generate unique certificate number
                    $certificate_number = generateCertificateNumber();

                    // Insert certificate record
                    $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                                VALUES (?, ?, CURDATE(), ?)";
                    $cert_stmt = mysqli_prepare($conn, $cert_sql);
                    mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $student['course_id']);
                    mysqli_stmt_execute($cert_stmt);

                    $_SESSION['message'] = 'Fee marked as paid and certificate generated successfully';
                } else {
                    $_SESSION['message'] = 'Fee marked as paid successfully';
                }
            } else {
                $_SESSION['message'] = 'Fee marked as paid successfully';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating fee status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to avoid form resubmission
    redirect(SITE_URL . 'admin/fees.php' . (isset($_GET['status']) ? '?status=' . $_GET['status'] : ''));
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filters
$query = "SELECT f.*, s.full_name, s.cnic, s.course_status, c.course_name, c.fee
          FROM fees f
          JOIN students s ON f.student_id = s.id
          LEFT JOIN courses c ON s.course_id = c.id";

if ($status_filter) {
    $query .= " WHERE f.status = '" . mysqli_real_escape_string($conn, $status_filter) . "'";
}

$query .= " ORDER BY f.created_at DESC";

// Execute query
$result = mysqli_query($conn, $query);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <!-- Modal Fix CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/modal-fix.css">
    <style>
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #2c3e50);
            color: white;
            min-height: 100vh;
            transition: var(--transition);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.3rem 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
        }

        .content {
            padding: 20px;
        }

        @media (max-width: 992px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1030;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .content {
                width: 100%;
            }
        }

        .filter-bar {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">Manage Fees</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Fees</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-home"></i> Visit Website
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Fee Stats -->
                <div class="row mb-4" data-aos="fade-up">
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="icon-box me-3">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-muted mb-1">Total Fees</h6>
                                        <?php
                                        $total_fees = mysqli_fetch_assoc(mysqli_query($conn, "SELECT SUM(amount) as total FROM fees"))['total'];
                                        ?>
                                        <h4 class="mb-0">Rs. <?php echo number_format($total_fees, 2); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="icon-box me-3" style="background: linear-gradient(135deg, #4cc9f0, #4361ee);">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-muted mb-1">Paid Fees</h6>
                                        <?php
                                        $paid_fees = mysqli_fetch_assoc(mysqli_query($conn, "SELECT SUM(amount) as total FROM fees WHERE status = 'paid'"))['total'];
                                        ?>
                                        <h4 class="mb-0">Rs. <?php echo number_format($paid_fees ? $paid_fees : 0, 2); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="icon-box me-3" style="background: linear-gradient(135deg, #f72585, #7209b7);">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-muted mb-1">Pending Fees</h6>
                                        <?php
                                        $pending_fees = mysqli_fetch_assoc(mysqli_query($conn, "SELECT SUM(amount) as total FROM fees WHERE status = 'pending'"))['total'];
                                        ?>
                                        <h4 class="mb-0">Rs. <?php echo number_format($pending_fees ? $pending_fees : 0, 2); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Bar -->
                <div class="filter-bar" data-aos="fade-up">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="fees.php" class="btn <?php echo $status_filter == '' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    All Fees
                                </a>
                                <a href="fees.php?status=pending" class="btn <?php echo $status_filter == 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Pending
                                </a>
                                <a href="fees.php?status=paid" class="btn <?php echo $status_filter == 'paid' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Paid
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search by name or CNIC...">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fees Table -->
                <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="feesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Student</th>
                                        <th>CNIC</th>
                                        <th>Course</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($result) > 0): ?>
                                        <?php $i = 1; while ($fee = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td><?php echo $fee['full_name']; ?></td>
                                                <td><?php echo $fee['cnic']; ?></td>
                                                <td><?php echo $fee['course_name']; ?></td>
                                                <td>Rs. <?php echo number_format($fee['amount'], 2); ?></td>
                                                <td>
                                                    <?php if ($fee['status'] == 'paid'): ?>
                                                        <span class="badge bg-success">Paid</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($fee['payment_date']): ?>
                                                        <?php echo formatDate($fee['payment_date']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="student-details.php?id=<?php echo $fee['student_id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        <?php if ($fee['status'] == 'pending'): ?>
                                                            <!-- Mark Paid Button (No Modal) -->
                                                            <a href="javascript:void(0)" onclick="confirmMarkPaid(<?php echo $fee['id']; ?>, <?php echo $fee['student_id']; ?>, '<?php echo $fee['full_name']; ?>', '<?php echo $fee['course_name']; ?>', '<?php echo number_format($fee['amount'], 2); ?>', '<?php echo $fee['course_status']; ?>')" class="btn btn-sm btn-success">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="py-5">
                                                    <i class="fas fa-money-bill-wave fa-4x text-muted mb-3"></i>
                                                    <h5>No fee records found</h5>
                                                    <?php if ($status_filter): ?>
                                                        <p class="text-muted">No fees with status: <?php echo ucfirst($status_filter); ?></p>
                                                        <a href="fees.php" class="btn btn-outline-primary mt-3">View All Fees</a>
                                                    <?php else: ?>
                                                        <p class="text-muted">No fee records available</p>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>

    <script>
        // Initialize AOS animation
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('feesTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');

                if (cells.length === 0) continue;

                let found = false;

                for (let j = 1; j < 4; j++) { // Search in student name, CNIC, and course columns
                    const cellText = cells[j].textContent.toLowerCase();

                    if (cellText.indexOf(searchValue) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.navbar-toggler');
        const sidebar = document.querySelector('.sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Close sidebar when clicking outside
            document.addEventListener('click', function(e) {
                if (sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }

        // Custom confirmation function without modal
        function confirmMarkPaid(feeId, studentId, studentName, courseName, amount, courseStatus) {
            let message = 'Are you sure you want to mark the fee as paid for ' + studentName + '?\n\n';
            message += 'Amount: Rs. ' + amount + '\n';
            message += 'Course: ' + courseName;

            if (courseStatus === 'completed') {
                message += '\n\nThis student has completed the course. Marking the fee as paid will generate a certificate.';
            }

            if (confirm(message)) {
                const status = new URLSearchParams(window.location.search).get('status') || '';
                const statusParam = status ? '&status=' + status : '';
                window.location.href = 'fees.php?action=mark_paid&fee_id=' + feeId + '&student_id=' + studentId + statusParam;
            }
        }
    </script>
</body>
</html>
