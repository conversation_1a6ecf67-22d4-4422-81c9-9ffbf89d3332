<?php
$page_title = "Student Details";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['message'] = 'Invalid student ID';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student_id = (int) $_GET['id'];
$conn = getDbConnection();

// Get student details
$sql = "SELECT s.*, c.course_name, c.duration, c.fee, u.username, u.email
        FROM students s
        LEFT JOIN courses c ON s.course_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $student_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = 'Student not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student = mysqli_fetch_assoc($result);

// Get fee status
$fee_result = mysqli_query($conn, "SELECT * FROM fees WHERE student_id = $student_id");
$fee = mysqli_fetch_assoc($fee_result);

// Get certificate if available
$cert_result = mysqli_query($conn, "SELECT * FROM certificates WHERE student_id = $student_id");
$certificate = mysqli_fetch_assoc($cert_result);

// Process actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Mark fee as paid
    if (isset($_POST['mark_paid'])) {
        $sql = "UPDATE fees SET status = 'paid', payment_date = CURDATE() WHERE student_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);

        if (mysqli_stmt_execute($stmt)) {
            // Check if course is completed to generate certificate
            if ($student['course_status'] == 'completed') {
                // Generate certificate if not already exists
                $cert_check = mysqli_query($conn, "SELECT id FROM certificates WHERE student_id = $student_id");

                if (mysqli_num_rows($cert_check) == 0) {
                    // Generate unique certificate number
                    $certificate_number = generateCertificateNumber();

                    // Insert certificate record
                    $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                                VALUES (?, ?, CURDATE(), ?)";
                    $cert_stmt = mysqli_prepare($conn, $cert_sql);
                    mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $student['course_id']);
                    mysqli_stmt_execute($cert_stmt);

                    $_SESSION['message'] = 'Fee marked as paid and certificate generated successfully';
                } else {
                    $_SESSION['message'] = 'Fee marked as paid successfully';
                }
            } else {
                $_SESSION['message'] = 'Fee marked as paid successfully';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating fee status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Mark course as completed
    if (isset($_POST['complete_course'])) {
        $sql = "UPDATE students SET course_status = 'completed' WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);

        if (mysqli_stmt_execute($stmt)) {
            // Check if fee is paid
            $fee_check = mysqli_query($conn, "SELECT status FROM fees WHERE student_id = $student_id");
            $fee_status = mysqli_fetch_assoc($fee_check)['status'];

            if ($fee_status == 'paid') {
                // Generate certificate if not already exists
                $cert_check = mysqli_query($conn, "SELECT id FROM certificates WHERE student_id = $student_id");

                if (mysqli_num_rows($cert_check) == 0) {
                    // Generate unique certificate number
                    $certificate_number = generateCertificateNumber();

                    // Insert certificate record
                    $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                                VALUES (?, ?, CURDATE(), ?)";
                    $cert_stmt = mysqli_prepare($conn, $cert_sql);
                    mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $student['course_id']);
                    mysqli_stmt_execute($cert_stmt);

                    $_SESSION['message'] = 'Course marked as completed and certificate generated successfully';
                } else {
                    $_SESSION['message'] = 'Course marked as completed successfully';
                }
            } else {
                $_SESSION['message'] = 'Course marked as completed. Certificate will be generated after fee payment';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating course status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh data
    redirect(SITE_URL . 'admin/student-details.php?id=' . $student_id);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #2c3e50);
            color: white;
            min-height: 100vh;
            transition: var(--transition);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.3rem 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
        }

        .content {
            padding: 20px;
        }

        @media (max-width: 992px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1030;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .content {
                width: 100%;
            }
        }

        .profile-photo-lg {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
            border: 5px solid #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .info-card {
            height: 100%;
        }

        .info-card .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-weight: 600;
        }

        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }

        .action-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">Student Details</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="students.php">Students</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $student['full_name']; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="students.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to Students
                        </a>
                        <a href="student-courses.php?id=<?php echo $student_id; ?>" class="btn btn-sm btn-primary me-2">
                            <i class="fas fa-book"></i> Manage Courses
                        </a>
                        <a href="delete-student.php?id=<?php echo $student_id; ?>" class="btn btn-sm btn-danger me-2">
                            <i class="fas fa-trash"></i> Delete Student
                        </a>
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-home"></i> Visit Website
                        </a>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Student Profile Header -->
                <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center mb-3 mb-md-0">
                                <?php if ($student['profile_photo']): ?>
                                    <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="profile-photo-lg">
                                <?php else: ?>
                                    <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="profile-photo-lg">
                                <?php endif; ?>
                            </div>
                            <div class="col-md-7">
                                <h3 class="mb-1"><?php echo $student['full_name']; ?></h3>
                                <p class="text-muted mb-2"><?php echo $student['cnic']; ?></p>
                                <p class="mb-2"><i class="fas fa-phone me-2 text-primary"></i> <?php echo $student['mobile']; ?></p>
                                <p class="mb-0"><i class="fas fa-calendar me-2 text-primary"></i> Registered on <?php echo formatDate($student['registration_date']); ?></p>
                            </div>
                            <div class="col-md-3 text-md-end">
                                <div class="d-flex flex-column align-items-md-end">
                                    <span class="badge bg-<?php echo $student['status'] == 'approved' ? 'success' : ($student['status'] == 'pending' ? 'warning' : 'danger'); ?> status-badge mb-2">
                                        <?php echo ucfirst($student['status']); ?>
                                    </span>
                                    <span class="badge bg-<?php echo $student['course_status'] == 'completed' ? 'success' : 'primary'; ?> status-badge">
                                        Course: <?php echo ucfirst($student['course_status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Student Information -->
                <div class="row" data-aos="fade-up">
                    <!-- Personal Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm info-card">
                            <div class="card-header">
                                <i class="fas fa-user me-2"></i> Personal Information
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <th width="40%">Full Name:</th>
                                        <td><?php echo $student['full_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Father's Name:</th>
                                        <td><?php echo $student['father_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>CNIC:</th>
                                        <td><?php echo $student['cnic']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Mobile:</th>
                                        <td><?php echo $student['mobile']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Address:</th>
                                        <td><?php echo $student['address']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Registration Date:</th>
                                        <td><?php echo formatDate($student['registration_date']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Course Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm info-card">
                            <div class="card-header">
                                <i class="fas fa-book me-2"></i> Course Information
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <th width="40%">Course:</th>
                                        <td><?php echo $student['course_name']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Duration:</th>
                                        <td><?php echo $student['duration']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Fee:</th>
                                        <td>Rs. <?php echo number_format($student['fee'], 2); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Fee Status:</th>
                                        <td>
                                            <?php if ($fee['status'] == 'paid'): ?>
                                                <span class="badge bg-success">Paid</span>
                                                <small class="text-muted d-block mt-1">Paid on: <?php echo formatDate($fee['payment_date']); ?></small>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Course Status:</th>
                                        <td>
                                            <?php if ($student['course_status'] == 'completed'): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">Ongoing</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php if ($certificate): ?>
                                        <tr>
                                            <th>Certificate:</th>
                                            <td>
                                                <span class="badge bg-info"><?php echo $certificate['certificate_number']; ?></span>
                                                <small class="text-muted d-block mt-1">Issued on: <?php echo formatDate($certificate['issue_date']); ?></small>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm info-card">
                            <div class="card-header">
                                <i class="fas fa-user-shield me-2"></i> Account Information
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <th width="40%">Username:</th>
                                        <td><?php echo $student['username']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email:</th>
                                        <td><?php echo $student['email']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Account Status:</th>
                                        <td>
                                            <?php if ($student['status'] == 'approved'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif ($student['status'] == 'pending'): ?>
                                                <span class="badge bg-warning">Pending</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm info-card">
                            <div class="card-header">
                                <i class="fas fa-cogs me-2"></i> Actions
                            </div>
                            <div class="card-body">
                                <div class="action-buttons">
                                    <?php if ($student['status'] == 'pending'): ?>
                                        <form method="post" action="students.php" class="d-inline">
                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                            <button type="submit" name="approve_student" class="btn btn-success">
                                                <i class="fas fa-check me-2"></i> Approve Student
                                            </button>
                                        </form>

                                        <form method="post" action="students.php" class="d-inline">
                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                            <button type="submit" name="reject_student" class="btn btn-danger">
                                                <i class="fas fa-times me-2"></i> Reject Student
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if ($student['status'] == 'approved'): ?>
                                        <?php if ($fee['status'] == 'pending'): ?>
                                            <form method="post" action="" class="d-inline">
                                                <button type="submit" name="mark_paid" class="btn btn-warning">
                                                    <i class="fas fa-money-bill-wave me-2"></i> Mark Fee as Paid
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if ($student['course_status'] == 'ongoing'): ?>
                                            <form method="post" action="" class="d-inline">
                                                <button type="submit" name="complete_course" class="btn btn-info">
                                                    <i class="fas fa-graduation-cap me-2"></i> Mark Course as Completed
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <a href="print-admission-form.php?id=<?php echo $student['id']; ?>" class="btn btn-primary" target="_blank">
                                        <i class="fas fa-print me-2"></i> Print Admission Form
                                    </a>

                                    <?php if ($certificate): ?>
                                        <a href="print-certificate.php?id=<?php echo $student['id']; ?>" class="btn btn-secondary" target="_blank">
                                            <i class="fas fa-certificate me-2"></i> View Certificate
                                        </a>
                                        <a href="<?php echo SITE_URL; ?>certificate.php?id=<?php echo $certificate['id']; ?>" class="btn btn-info" target="_blank">
                                            <i class="fas fa-qrcode me-2"></i> View Certificate with QR
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- Modal Fix JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/modal-fix.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>

    <script>
        // Initialize AOS animation
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.navbar-toggler');
        const sidebar = document.querySelector('.sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Close sidebar when clicking outside
            document.addEventListener('click', function(e) {
                if (sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }


    </script>
</body>
</html>
