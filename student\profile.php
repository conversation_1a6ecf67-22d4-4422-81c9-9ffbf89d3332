<?php
$page_title = "My Profile";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/logout.php');
}

// Get course details
$conn = getDbConnection();
$course_result = mysqli_query($conn, "SELECT * FROM courses WHERE id = {$student['course_id']}");
$course = mysqli_fetch_assoc($course_result);

// Get fee details
$fee_result = mysqli_query($conn, "SELECT * FROM fees WHERE student_id = {$student['id']}");
$fee = mysqli_fetch_assoc($fee_result);

// Get certificate if available
$cert_result = mysqli_query($conn, "SELECT * FROM certificates WHERE student_id = {$student['id']}");
$certificate = mysqli_fetch_assoc($cert_result);

// Handle download form action
if (isset($_GET['action']) && $_GET['action'] == 'download_form') {
    // Redirect to student's print-admission-form.php
    header("Location: " . SITE_URL . "student/print-admission-form.php");
    exit;
}

// Process profile update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $mobile = clean($_POST['mobile']);
    $address = clean($_POST['address']);

    // Update profile
    $sql = "UPDATE students SET mobile = ?, address = ? WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ssi", $mobile, $address, $student['id']);

    if (mysqli_stmt_execute($stmt)) {
        // Handle profile photo upload
        if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['size'] > 0) {
            $upload_result = uploadProfilePhoto($_FILES['profile_photo'], $student['id']);

            if ($upload_result['success']) {
                $photo_name = $upload_result['filename'];

                // Update profile photo in database
                $sql = "UPDATE students SET profile_photo = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "si", $photo_name, $student['id']);
                mysqli_stmt_execute($stmt);
            } else {
                $_SESSION['message'] = $upload_result['message'];
                $_SESSION['message_type'] = 'danger';
                redirect(SITE_URL . 'student/profile.php');
            }
        }

        $_SESSION['message'] = 'Profile updated successfully';
        $_SESSION['message_type'] = 'success';
        redirect(SITE_URL . 'student/profile.php');
    } else {
        $_SESSION['message'] = 'Error updating profile: ' . mysqli_error($conn);
        $_SESSION['message_type'] = 'danger';
        redirect(SITE_URL . 'student/profile.php');
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-dark: #3a56d4;
            --secondary-color: #7209b7;
            --accent-color: #f72585;
            --success-color: #4cc9f0;
            --warning-color: #f9c74f;
            --danger-color: #f94144;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --gray-color: #6c757d;
            --border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f2f5;
            line-height: 1.7;
            color: var(--dark-color);
            overflow-x: hidden;
        }

        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #343a40;
            color: white;
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: 0.5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.2rem 0.5rem;
            transition: var(--transition);
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: var(--primary-color);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            color: rgba(255, 255, 255, 0.6);
        }

        .sidebar .nav-link.active i {
            color: #fff;
        }

        .content {
            margin-top: 20px;
        }

        .profile-photo {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .profile-photo-lg {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            border-radius: 0 !important;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .badge {
            padding: 5px 10px;
            border-radius: 30px;
            font-weight: 500;
        }

        .form-control {
            border-radius: var(--border-radius);
            padding: 0.6rem 1rem;
            border: 1px solid #dee2e6;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>student/dashboard.php">
                <img src="<?php echo SITE_URL; ?>assets/images/logo.png" alt="Logo" height="30" class="d-inline-block align-text-top me-2">
                <?php echo SITE_NAME; ?> - Student Portal
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo SITE_URL; ?>student/dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                            <?php echo isset($_SESSION['username']) ? $_SESSION['username'] : $student['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li>
                                <a class="dropdown-item" href="<?php echo SITE_URL; ?>student/profile.php">
                                    <i class="fas fa-user me-2"></i> My Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo SITE_URL; ?>student/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <?php if ($student['profile_photo']): ?>
                        <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="profile-photo mb-3" onerror="this.src='<?php echo SITE_URL; ?>assets/images/default-profile.png'">
                    <?php else: ?>
                        <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="profile-photo mb-3">
                    <?php endif; ?>
                    <h5 class="text-white"><?php echo $student['full_name']; ?></h5>
                    <p class="text-light opacity-75">Student Portal</p>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>student/dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>student/profile.php">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'enroll-course.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>student/enroll-course.php">
                            <i class="fas fa-book"></i> Enroll in Course
                        </a>
                    </li>
                    <?php
                    // Check if certificate exists
                    $cert_check = mysqli_query(getDbConnection(), "SELECT * FROM certificates WHERE student_id = {$student['id']}");
                    $has_certificate = mysqli_num_rows($cert_check) > 0;

                    if ($has_certificate):
                    ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'certificate.php' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>student/certificate.php">
                                <i class="fas fa-certificate"></i> My Certificate
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item mt-4">
                        <a class="nav-link text-danger" href="<?php echo SITE_URL; ?>student/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">My Profile</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-home"></i> Visit Website
                        </a>
                    </div>
                </div>
            </div>

            <?php
            // Show session messages
            if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
                $message = $_SESSION['message'];
                $type = $_SESSION['message_type'];

                // Map message type to Bootstrap alert class
                $alert_class = 'alert-info';
                switch ($type) {
                    case 'success':
                        $alert_class = 'alert-success';
                        break;
                    case 'danger':
                        $alert_class = 'alert-danger';
                        break;
                    case 'warning':
                        $alert_class = 'alert-warning';
                        break;
                }

                echo '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">';
                echo $message;
                echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
                echo '</div>';

                // Clear the message
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
            }
            ?>

            <!-- Student Profile -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <?php if ($student['profile_photo']): ?>
                                <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="img-thumbnail profile-photo-lg mb-3">
                            <?php else: ?>
                                <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="img-thumbnail profile-photo-lg mb-3">
                            <?php endif; ?>

                            <h4><?php echo $student['full_name']; ?></h4>
                            <p class="text-muted"><?php echo $student['cnic']; ?></p>

                            <div class="mt-3">
                                <a href="<?php echo SITE_URL; ?>student/print-admission-form.php" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-print"></i> Print Admission Form
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Personal Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th width="30%">Registration No:</th>
                                    <td>CTI-<?php echo str_pad($student['id'], 4, '0', STR_PAD_LEFT); ?></td>
                                </tr>
                                <tr>
                                    <th>Full Name:</th>
                                    <td><?php echo $student['full_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>Father's Name:</th>
                                    <td><?php echo $student['father_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>CNIC:</th>
                                    <td><?php echo $student['cnic']; ?></td>
                                </tr>
                                <tr>
                                    <th>Mobile:</th>
                                    <td><?php echo $student['mobile']; ?></td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td><?php echo $student['address']; ?></td>
                                </tr>
                                <tr>
                                    <th>Registration Date:</th>
                                    <td><?php echo formatDate($student['registration_date']); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Course Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th width="30%">Course:</th>
                                    <td><?php echo $course['course_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>Duration:</th>
                                    <td><?php echo $course['duration']; ?></td>
                                </tr>
                                <tr>
                                    <th>Fee:</th>
                                    <td>Rs. <?php echo number_format($course['fee']); ?></td>
                                </tr>
                                <tr>
                                    <th>Fee Status:</th>
                                    <td>
                                        <?php if ($fee['status'] == 'paid'): ?>
                                            <span class="badge bg-success">Paid</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Course Status:</th>
                                    <td>
                                        <?php if ($student['course_status'] == 'completed'): ?>
                                            <span class="badge bg-success">Completed</span>
                                        <?php else: ?>
                                            <span class="badge bg-primary">Ongoing</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update Profile Form -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Update Profile</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="mobile" class="form-label">Mobile Number</label>
                                <input type="text" class="form-control" id="mobile" name="mobile" value="<?php echo $student['mobile']; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="profile_photo" class="form-label">Profile Photo</label>
                                <input type="file" class="form-control" id="profile_photo" name="profile_photo">
                                <small class="text-muted">Upload a new photo (optional)</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required><?php echo $student['address']; ?></textarea>
                        </div>
                        <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Auto-hide alerts after 5 seconds
        window.setTimeout(function() {
            document.querySelectorAll('.alert').forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
