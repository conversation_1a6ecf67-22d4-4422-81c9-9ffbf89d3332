<?php
$page_title = "Student Courses";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['message'] = 'Invalid student ID';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student_id = (int) $_GET['id'];
$conn = getDbConnection();

// Get student details
$sql = "SELECT * FROM students WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $student_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = 'Student not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student = mysqli_fetch_assoc($result);

// Get student's courses
$courses_query = "SELECT sc.*, c.course_name, c.duration, c.fee
                 FROM student_courses sc
                 JOIN courses c ON sc.course_id = c.id
                 WHERE sc.student_id = ?";
$stmt = mysqli_prepare($conn, $courses_query);
mysqli_stmt_bind_param($stmt, "i", $student_id);
mysqli_stmt_execute($stmt);
$courses_result = mysqli_stmt_get_result($stmt);

// Get all active courses for enrollment
$all_courses_query = "SELECT * FROM courses WHERE status = 'active' ORDER BY course_name ASC";
$all_courses_result = mysqli_query($conn, $all_courses_query);

// Process actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new course
    if (isset($_POST['add_course'])) {
        $course_id = (int) $_POST['course_id'];

        // Check if course exists
        $course_check_query = "SELECT * FROM courses WHERE id = ? AND status = 'active'";
        $stmt = mysqli_prepare($conn, $course_check_query);
        mysqli_stmt_bind_param($stmt, "i", $course_id);
        mysqli_stmt_execute($stmt);
        $course_result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($course_result) == 0) {
            $_SESSION['message'] = 'Invalid course selected';
            $_SESSION['message_type'] = 'danger';
            redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
        }

        $course = mysqli_fetch_assoc($course_result);

        // Check if student is already enrolled in this course
        $check_query = "SELECT * FROM student_courses WHERE student_id = ? AND course_id = ?";
        $stmt = mysqli_prepare($conn, $check_query);
        mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);
        mysqli_stmt_execute($stmt);
        $check_result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($check_result) > 0) {
            $_SESSION['message'] = 'Student is already enrolled in this course';
            $_SESSION['message_type'] = 'warning';
            redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
        }

        // Enroll student in the course
        $enrollment_date = date('Y-m-d');
        $status = 'ongoing';

        $enroll_query = "INSERT INTO student_courses (student_id, course_id, enrollment_date, status) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $enroll_query);
        mysqli_stmt_bind_param($stmt, "iiss", $student_id, $course_id, $enrollment_date, $status);

        if (mysqli_stmt_execute($stmt)) {
            // Create fee record for the new course
            $fee_query = "INSERT INTO fees (student_id, course_id, amount, status, payment_date) VALUES (?, ?, ?, 'pending', NULL)";
            $stmt = mysqli_prepare($conn, $fee_query);
            mysqli_stmt_bind_param($stmt, "iid", $student_id, $course_id, $course['fee']);
            mysqli_stmt_execute($stmt);

            $_SESSION['message'] = 'Successfully enrolled student in ' . $course['course_name'] . ' course';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error enrolling student in course: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }

        redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
    }

    // Mark course as completed
    if (isset($_POST['complete_course'])) {
        $course_id = (int) $_POST['course_id'];

        $sql = "UPDATE student_courses SET status = 'completed' WHERE student_id = ? AND course_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);

        if (mysqli_stmt_execute($stmt)) {
            // Check if fee is paid
            $fee_check = mysqli_query($conn, "SELECT status FROM fees WHERE student_id = $student_id AND course_id = $course_id");
            $fee_status = mysqli_fetch_assoc($fee_check)['status'];

            if ($fee_status == 'paid') {
                // Generate certificate if not already exists
                $cert_check = mysqli_query($conn, "SELECT id FROM certificates WHERE student_id = $student_id AND course_id = $course_id");

                if (mysqli_num_rows($cert_check) == 0) {
                    // Generate unique certificate number
                    $certificate_number = generateCertificateNumber();

                    // Insert certificate record
                    $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                                VALUES (?, ?, CURDATE(), ?)";
                    $cert_stmt = mysqli_prepare($conn, $cert_sql);
                    mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $course_id);
                    mysqli_stmt_execute($cert_stmt);

                    $_SESSION['message'] = 'Course marked as completed and certificate generated successfully';
                } else {
                    $_SESSION['message'] = 'Course marked as completed successfully';
                }
            } else {
                $_SESSION['message'] = 'Course marked as completed. Certificate will be generated after fee payment';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating course status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }

        redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
    }

    // Mark fee as paid
    if (isset($_POST['mark_paid'])) {
        $course_id = (int) $_POST['course_id'];

        // First check if fee record exists
        $check_fee = "SELECT * FROM fees WHERE student_id = ? AND course_id = ?";
        $check_stmt = mysqli_prepare($conn, $check_fee);
        mysqli_stmt_bind_param($check_stmt, "ii", $student_id, $course_id);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);

        if (mysqli_num_rows($check_result) > 0) {
            // Update existing fee record
            $sql = "UPDATE fees SET status = 'paid', payment_date = CURDATE() WHERE student_id = ? AND course_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);
            $update_success = mysqli_stmt_execute($stmt);
        } else {
            // Get course fee
            $course_fee_query = "SELECT fee FROM courses WHERE id = ?";
            $fee_stmt = mysqli_prepare($conn, $course_fee_query);
            mysqli_stmt_bind_param($fee_stmt, "i", $course_id);
            mysqli_stmt_execute($fee_stmt);
            $fee_result = mysqli_stmt_get_result($fee_stmt);
            $course_fee = mysqli_fetch_assoc($fee_result)['fee'];

            // Create new fee record
            $sql = "INSERT INTO fees (student_id, course_id, amount, status, payment_date) VALUES (?, ?, ?, 'paid', CURDATE())";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "iid", $student_id, $course_id, $course_fee);
            $update_success = mysqli_stmt_execute($stmt);
        }

        if ($update_success) {
            // Check if course is completed to generate certificate
            $course_check = mysqli_query($conn, "SELECT status FROM student_courses WHERE student_id = $student_id AND course_id = $course_id");
            $course_status = mysqli_fetch_assoc($course_check)['status'];

            if ($course_status == 'completed') {
                // Generate certificate if not already exists
                $cert_check = mysqli_query($conn, "SELECT id FROM certificates WHERE student_id = $student_id AND course_id = $course_id");

                if (mysqli_num_rows($cert_check) == 0) {
                    // Generate unique certificate number
                    $certificate_number = generateCertificateNumber();

                    // Insert certificate record
                    $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                                VALUES (?, ?, CURDATE(), ?)";
                    $cert_stmt = mysqli_prepare($conn, $cert_sql);
                    mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $course_id);
                    mysqli_stmt_execute($cert_stmt);

                    $_SESSION['message'] = 'Fee marked as paid and certificate generated successfully';
                } else {
                    $_SESSION['message'] = 'Fee marked as paid successfully';
                }
            } else {
                $_SESSION['message'] = 'Fee marked as paid successfully';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating fee status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }

        redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #2c3e50);
            color: white;
            min-height: 100vh;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.3rem 0.5rem;
            border-radius: 0.5rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, #4361ee, #7209b7);
        }
        .sidebar .nav-link i {
            margin-right: 0.8rem;
        }
        .content {
            padding: 20px;
        }
        .course-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .course-card .card-header {
            background: linear-gradient(135deg, #4361ee, #7209b7);
            color: white;
            border-radius: 10px 10px 0 0;
            font-weight: 600;
        }
        .course-status {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">Student Courses</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="students.php">Students</a></li>
                                <li class="breadcrumb-item"><a href="student-details.php?id=<?php echo $student_id; ?>"><?php echo $student['full_name']; ?></a></li>
                                <li class="breadcrumb-item active" aria-current="page">Courses</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="student-details.php?id=<?php echo $student_id; ?>" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to Student Details
                        </a>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                            <i class="fas fa-plus"></i> Add New Course
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Student Courses -->
                <div class="row">
                    <?php if (mysqli_num_rows($courses_result) > 0): ?>
                        <?php while ($course = mysqli_fetch_assoc($courses_result)): ?>
                            <?php
                            // Get fee status
                            $fee_query = "SELECT * FROM fees WHERE student_id = ? AND course_id = ?";
                            $stmt = mysqli_prepare($conn, $fee_query);
                            mysqli_stmt_bind_param($stmt, "ii", $student_id, $course['course_id']);
                            mysqli_stmt_execute($stmt);
                            $fee_result = mysqli_stmt_get_result($stmt);
                            $fee = mysqli_fetch_assoc($fee_result);

                            // Get certificate if available
                            $cert_query = "SELECT * FROM certificates WHERE student_id = ? AND course_id = ?";
                            $stmt = mysqli_prepare($conn, $cert_query);
                            mysqli_stmt_bind_param($stmt, "ii", $student_id, $course['course_id']);
                            mysqli_stmt_execute($stmt);
                            $cert_result = mysqli_stmt_get_result($stmt);
                            $certificate = mysqli_fetch_assoc($cert_result);
                            ?>
                            <div class="col-md-6 mb-4">
                                <div class="card course-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><?php echo $course['course_name']; ?></h5>
                                        <span class="course-status badge bg-<?php echo $course['status'] == 'completed' ? 'success' : 'primary'; ?>">
                                            <?php echo ucfirst($course['status']); ?>
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <p><strong>Duration:</strong> <?php echo $course['duration']; ?></p>
                                                <p><strong>Fee:</strong> Rs. <?php echo number_format($course['fee'], 2); ?></p>
                                                <p><strong>Enrollment Date:</strong> <?php echo formatDate($course['enrollment_date']); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p>
                                                    <strong>Fee Status:</strong>
                                                    <?php if (isset($fee) && $fee && $fee['status'] == 'paid'): ?>
                                                        <span class="badge bg-success">Paid</span>
                                                        <small class="text-muted d-block">Paid on: <?php echo formatDate($fee['payment_date']); ?></small>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php endif; ?>
                                                </p>
                                                <?php if ($certificate): ?>
                                                    <p>
                                                        <strong>Certificate:</strong>
                                                        <span class="badge bg-info"><?php echo $certificate['certificate_number']; ?></span>
                                                        <small class="text-muted d-block">Issued on: <?php echo formatDate($certificate['issue_date']); ?></small>
                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between">
                                            <?php if ($course['status'] == 'ongoing'): ?>
                                                <form method="post" action="">
                                                    <input type="hidden" name="course_id" value="<?php echo $course['course_id']; ?>">
                                                    <button type="submit" name="complete_course" class="btn btn-success">
                                                        <i class="fas fa-check-circle"></i> Mark as Completed
                                                    </button>
                                                </form>
                                            <?php endif; ?>

                                            <?php if (!isset($fee) || !$fee || $fee['status'] == 'pending'): ?>
                                                <form method="post" action="">
                                                    <input type="hidden" name="course_id" value="<?php echo $course['course_id']; ?>">
                                                    <button type="submit" name="mark_paid" class="btn btn-primary">
                                                        <i class="fas fa-money-bill-wave"></i> Mark Fee as Paid
                                                    </button>
                                                </form>
                                            <?php endif; ?>

                                            <?php if ($certificate): ?>
                                                <a href="print-certificate.php?id=<?php echo $certificate['id']; ?>" target="_blank" class="btn btn-info">
                                                    <i class="fas fa-print"></i> Print Certificate
                                                </a>
                                            <?php elseif (isset($fee) && $fee && $fee['status'] == 'paid' && $course['status'] == 'completed'): ?>
                                                <a href="generate-certificate.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course['course_id']; ?>" class="btn btn-outline-info">
                                                    <i class="fas fa-certificate"></i> Generate Certificate
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No courses found for this student. Use the "Add New Course" button to enroll the student in a course.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Course Modal -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" aria-labelledby="addCourseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCourseModalLabel">Add New Course</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="course_id" class="form-label">Select Course</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- Select Course --</option>
                                <?php while ($course = mysqli_fetch_assoc($all_courses_result)): ?>
                                    <option value="<?php echo $course['id']; ?>"><?php echo $course['course_name']; ?> (Rs. <?php echo number_format($course['fee'], 2); ?>)</option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="add_course" class="btn btn-primary">Add Course</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
