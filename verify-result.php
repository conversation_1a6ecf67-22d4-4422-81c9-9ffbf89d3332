<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Verification - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Poppins', sans-serif;
        }
        .verification-container {
            max-width: 800px;
            margin: 50px auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .verification-header {
            background: linear-gradient(135deg, #4361ee, #7209b7);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .verification-content {
            padding: 30px;
        }
        .verification-result {
            text-align: center;
            margin-bottom: 30px;
        }
        .verification-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        .verification-icon.success {
            color: #28a745;
        }
        .verification-icon.error {
            color: #dc3545;
        }
        .certificate-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .certificate-details h3 {
            color: #4361ee;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        .detail-row {
            margin-bottom: 15px;
            display: flex;
        }
        .detail-label {
            font-weight: 600;
            width: 150px;
            color: #6c757d;
        }
        .detail-value {
            flex: 1;
        }
        .btn-view-certificate {
            background: linear-gradient(135deg, #4361ee, #7209b7);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 500;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .btn-view-certificate:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <h1>Certificate Verification</h1>
            <p>Verify the authenticity of certificates issued by <?php echo SITE_NAME; ?></p>
        </div>
        
        <div class="verification-content">
            <?php if (isset($error)): ?>
                <!-- Error Result -->
                <div class="verification-result">
                    <div class="verification-icon error">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <h2>Verification Failed</h2>
                    <p class="text-danger"><?php echo $error; ?></p>
                    <p>The certificate could not be verified. Please check the certificate number and try again.</p>
                    
                    <div class="mt-4">
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">Go to Homepage</a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Success Result -->
                <div class="verification-result">
                    <div class="verification-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2>Certificate Verified</h2>
                    <p class="text-success">This certificate is valid and was issued by <?php echo SITE_NAME; ?>.</p>
                </div>
                
                <!-- Certificate Details -->
                <div class="certificate-details">
                    <h3>Certificate Details</h3>
                    
                    <div class="detail-row">
                        <div class="detail-label">Certificate No:</div>
                        <div class="detail-value"><?php echo $certificate['certificate_number']; ?></div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">Student Name:</div>
                        <div class="detail-value"><?php echo $certificate['full_name']; ?></div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">Father's Name:</div>
                        <div class="detail-value"><?php echo $certificate['father_name']; ?></div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">CNIC:</div>
                        <div class="detail-value"><?php echo $certificate['cnic']; ?></div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">Course:</div>
                        <div class="detail-value"><?php echo $certificate['course_name']; ?> (<?php echo $certificate['duration']; ?>)</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label">Issue Date:</div>
                        <div class="detail-value"><?php echo date('d-m-Y', strtotime($certificate['issue_date'])); ?></div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="<?php echo SITE_URL; ?>certificate.php?id=<?php echo $certificate['id']; ?>" class="btn btn-view-certificate">
                            <i class="fas fa-certificate me-2"></i> View Certificate
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
