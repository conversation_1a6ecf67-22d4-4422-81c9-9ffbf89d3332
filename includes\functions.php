<?php
require_once 'db.php';

// Clean input data
function clean($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Validate CNIC format (12345-1234567-1)
function validateCNIC($cnic) {
    return preg_match('/^[0-9]{5}-[0-9]{7}-[0-9]{1}$/', $cnic);
}

// Check if CNIC already exists
function cnicExists($cnic) {
    $conn = getDbConnection();
    $cnic = mysqli_real_escape_string($conn, $cnic);

    $result = mysqli_query($conn, "SELECT id FROM students WHERE cnic = '$cnic'");

    return mysqli_num_rows($result) > 0;
}

// Generate unique certificate number
function generateCertificateNumber() {
    $prefix = 'CTI-';
    $year = date('Y');
    $random = mt_rand(1000, 9999);

    return $prefix . $year . '-' . $random;
}

// Upload file
function uploadFile($file, $destination, $allowed_types = ['jpg', 'jpeg', 'png']) {
    // Check if file was uploaded without errors
    if ($file['error'] == 0) {
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];

        // Get file extension
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Check if file type is allowed
        if (in_array($file_ext, $allowed_types)) {
            // Generate unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $upload_path = $destination . $new_file_name;

            // Create directory if it doesn't exist
            if (!file_exists($destination)) {
                mkdir($destination, 0777, true);
            }

            // Move uploaded file
            if (move_uploaded_file($file_tmp, $upload_path)) {
                return $new_file_name;
            }
        }
    }

    return false;
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
}

// Check if user is student
function isStudent() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'student';
}

// Redirect to URL
function redirect($url) {
    // Clear any output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Redirect
    header("Location: $url");
    exit();
}

// Get all active courses
function getActiveCourses() {
    $conn = getDbConnection();
    $result = mysqli_query($conn, "SELECT * FROM courses WHERE status = 'active' ORDER BY course_name");

    $courses = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $courses[] = $row;
    }

    return $courses;
}

// Get all courses for homepage
function getAllCoursesForHomepage() {
    $conn = getDbConnection();
    $result = mysqli_query($conn, "SELECT * FROM courses ORDER BY course_name");

    $courses = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $courses[] = $row;
    }

    return $courses;
}

// Get student by ID
function getStudentById($id) {
    $conn = getDbConnection();
    $id = (int) $id;

    $result = mysqli_query($conn, "SELECT s.*, c.course_name, c.duration, c.fee
                                   FROM students s
                                   LEFT JOIN courses c ON s.course_id = c.id
                                   WHERE s.id = $id");

    return mysqli_fetch_assoc($result);
}

// Get student by user ID
function getStudentByUserId($user_id) {
    $conn = getDbConnection();
    $user_id = (int) $user_id;

    $result = mysqli_query($conn, "SELECT s.*, c.course_name, c.duration, c.fee
                                   FROM students s
                                   LEFT JOIN courses c ON s.course_id = c.id
                                   WHERE s.user_id = $user_id");

    return mysqli_fetch_assoc($result);
}

// Get student by CNIC
function getStudentByCNIC($cnic) {
    $conn = getDbConnection();
    $cnic = mysqli_real_escape_string($conn, $cnic);

    $result = mysqli_query($conn, "SELECT s.*, c.course_name, c.duration, c.fee
                                   FROM students s
                                   LEFT JOIN courses c ON s.course_id = c.id
                                   WHERE s.cnic = '$cnic'");

    return mysqli_fetch_assoc($result);
}

// Get fee status for student
function getFeeStatus($student_id) {
    $conn = getDbConnection();
    $student_id = (int) $student_id;

    $result = mysqli_query($conn, "SELECT * FROM fees WHERE student_id = $student_id");

    return mysqli_fetch_assoc($result);
}

// Get certificate for student
function getStudentCertificate($student_id) {
    $conn = getDbConnection();
    $student_id = (int) $student_id;

    $result = mysqli_query($conn, "SELECT * FROM certificates WHERE student_id = $student_id");

    return mysqli_fetch_assoc($result);
}

// Get all certificates for student
function getAllStudentCertificates($student_id) {
    $conn = getDbConnection();
    $student_id = (int) $student_id;

    $result = mysqli_query($conn, "SELECT c.*, co.course_name, co.duration
                                  FROM certificates c
                                  JOIN courses co ON c.course_id = co.id
                                  WHERE c.student_id = $student_id
                                  ORDER BY c.issue_date DESC");

    $certificates = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $certificates[] = $row;
    }

    return $certificates;
}

// Get certificate by number
function getCertificateByNumber($certificate_number) {
    $conn = getDbConnection();
    $certificate_number = mysqli_real_escape_string($conn, $certificate_number);

    $result = mysqli_query($conn, "SELECT c.*, s.full_name, s.cnic, s.father_name, co.course_name
                                   FROM certificates c
                                   JOIN students s ON c.student_id = s.id
                                   JOIN courses co ON c.course_id = co.id
                                   WHERE c.certificate_number = '$certificate_number'");

    return mysqli_fetch_assoc($result);
}

// Get recent posts
function getRecentPosts($limit = 5) {
    $conn = getDbConnection();
    $limit = (int) $limit;

    $result = mysqli_query($conn, "SELECT * FROM posts WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit");

    $posts = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $posts[] = $row;
    }

    return $posts;
}

// Format date
function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

// Generate placeholder image URL
function getPlaceholderImage($width = 300, $height = 300, $text = 'CTI Marot', $bg = 'eeeeee', $color = '333333') {
    // Make sure we're using the correct path to placeholder.php
    return SITE_URL . 'placeholder.php?width=' . $width . '&height=' . $height . '&text=' . urlencode($text) . '&bg=' . $bg . '&color=' . $color;
}

// Show session messages
function showMessage() {
    if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'];

        // Map message type to Bootstrap alert class
        $alert_class = 'alert-info';
        switch ($type) {
            case 'success':
                $alert_class = 'alert-success';
                break;
            case 'danger':
                $alert_class = 'alert-danger';
                break;
            case 'warning':
                $alert_class = 'alert-warning';
                break;
        }

        echo '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">';
        echo $message;
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        echo '</div>';

        // Clear the message
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
}
?>
