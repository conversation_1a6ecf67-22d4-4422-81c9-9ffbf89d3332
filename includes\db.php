<?php
require_once 'config.php';

// Create database connection
function getDbConnection() {
    static $conn;
    
    if ($conn === null) {
        $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if (!$conn) {
            die("Connection failed: " . mysqli_connect_error());
        }
        
        mysqli_set_charset($conn, "utf8");
    }
    
    return $conn;
}

// Create database if it doesn't exist
function createDatabase() {
    $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS);
    
    if (!$conn) {
        die("Connection failed: " . mysqli_connect_error());
    }
    
    $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
    
    if (mysqli_query($conn, $sql)) {
        return true;
    } else {
        die("Error creating database: " . mysqli_error($conn));
    }
    
    mysqli_close($conn);
}

// Create tables
function createTables() {
    $conn = getDbConnection();
    
    // Users table (for admin and students)
    $sql_users = "CREATE TABLE IF NOT EXISTS users (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL,
        role ENUM('admin', 'student') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Courses table
    $sql_courses = "CREATE TABLE IF NOT EXISTS courses (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        course_name VARCHAR(100) NOT NULL,
        duration VARCHAR(50) NOT NULL,
        fee DECIMAL(10,2) NOT NULL,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Students table
    $sql_students = "CREATE TABLE IF NOT EXISTS students (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11) UNSIGNED,
        full_name VARCHAR(100) NOT NULL,
        cnic VARCHAR(15) NOT NULL UNIQUE,
        father_name VARCHAR(100) NOT NULL,
        mobile VARCHAR(15) NOT NULL,
        address TEXT NOT NULL,
        course_id INT(11) UNSIGNED,
        profile_photo VARCHAR(255),
        registration_date DATE,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        course_status ENUM('ongoing', 'completed') DEFAULT 'ongoing',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL
    )";
    
    // Fees table
    $sql_fees = "CREATE TABLE IF NOT EXISTS fees (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) UNSIGNED,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE,
        status ENUM('pending', 'paid') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )";
    
    // Certificates table
    $sql_certificates = "CREATE TABLE IF NOT EXISTS certificates (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) UNSIGNED,
        certificate_number VARCHAR(50) NOT NULL UNIQUE,
        issue_date DATE NOT NULL,
        course_id INT(11) UNSIGNED,
        file_path VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL
    )";
    
    // Posts table
    $sql_posts = "CREATE TABLE IF NOT EXISTS posts (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        image VARCHAR(255),
        status ENUM('published', 'draft') DEFAULT 'published',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Execute queries
    if (!mysqli_query($conn, $sql_users)) {
        die("Error creating users table: " . mysqli_error($conn));
    }
    
    if (!mysqli_query($conn, $sql_courses)) {
        die("Error creating courses table: " . mysqli_error($conn));
    }
    
    if (!mysqli_query($conn, $sql_students)) {
        die("Error creating students table: " . mysqli_error($conn));
    }
    
    if (!mysqli_query($conn, $sql_fees)) {
        die("Error creating fees table: " . mysqli_error($conn));
    }
    
    if (!mysqli_query($conn, $sql_certificates)) {
        die("Error creating certificates table: " . mysqli_error($conn));
    }
    
    if (!mysqli_query($conn, $sql_posts)) {
        die("Error creating posts table: " . mysqli_error($conn));
    }
    
    // Create default admin user
    $admin_exists = mysqli_query($conn, "SELECT id FROM users WHERE username = 'admin'");
    
    if (mysqli_num_rows($admin_exists) == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_admin = "INSERT INTO users (username, password, email, role) 
                      VALUES ('admin', '$admin_password', '<EMAIL>', 'admin')";
        
        if (!mysqli_query($conn, $sql_admin)) {
            die("Error creating admin user: " . mysqli_error($conn));
        }
    }
    
    return true;
}
?>
