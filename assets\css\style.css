/* Ultra Modern CSS for City Technical Institute Marot */

/* CSS Variables */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --dark-color: #1a202c;
    --light-color: #f7fafc;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-600);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Modern Button Styles */
.btn {
    border-radius: var(--border-radius);
    padding: 12px 24px;
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1rem;
    border-radius: var(--border-radius-lg);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.75rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Header styles */
header {
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.6rem;
    color: white !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Hero section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 120px 0;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.7), rgba(114, 9, 183, 0.7));
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: fadeInUp 1s ease;
}

.hero .btn {
    animation: fadeIn 1.5s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Card styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    margin-bottom: 25px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Form styles */
.form-container {
    background-color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.form-control {
    border-radius: var(--border-radius);
    padding: 0.6rem 1rem;
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.input-group-text {
    border-radius: var(--border-radius);
    background-color: #f8f9fa;
}

/* Dashboard styles */
.dashboard-stats .card {
    border-radius: var(--border-radius);
    border: none;
    overflow: hidden;
}

.dashboard-stats .card-body {
    padding: 1.5rem;
    position: relative;
}

.dashboard-stats .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--gray-color);
}

.dashboard-stats .card-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.dashboard-stats .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
}

/* Sidebar styles */
.sidebar {
    background: linear-gradient(180deg, var(--dark-color), #2c3e50);
    color: white;
    min-height: 100vh;
    transition: var(--transition);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.8rem 1rem;
    margin: 0.3rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.sidebar .nav-link i {
    margin-right: 0.8rem;
    font-size: 1.1rem;
}

/* Certificate styles */
.certificate {
    background-color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 50px;
    position: relative;
    font-family: 'Times New Roman', Times, serif;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.certificate::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 15px solid transparent;
    border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) 1;
    pointer-events: none;
}

.certificate::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../images/logo-watermark.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 50%;
    opacity: 0.05;
    pointer-events: none;
}

.certificate h1 {
    font-size: 2.5rem;
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 30px;
}

.certificate .certificate-content {
    font-size: 1.2rem;
    line-height: 2;
}

.certificate .signature {
    margin-top: 50px;
    text-align: right;
}

/* Profile photo */
.profile-photo {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* News/Posts styles */
.news-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.news-item:hover {
    transform: translateX(5px);
}

.news-item:last-child {
    border-bottom: none;
}

.news-date {
    color: var(--gray-color);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.news-date i {
    margin-right: 0.5rem;
}

/* Alert styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

/* Footer styles */
footer {
    background: linear-gradient(135deg, var(--dark-color), #2c3e50);
    color: white;
    padding: 3rem 0 1.5rem;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

footer a:hover {
    color: white;
    text-decoration: none;
}

footer h5 {
    color: white;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* New Components Styles */

/* Section Title */
.section-title h2 {
    font-weight: 700;
    position: relative;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.title-line {
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    margin-bottom: 20px;
}

.section-title .title-line.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

/* Feature Box */
.feature-box {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    color: var(--primary-color);
    transition: var(--transition);
}

.feature-box:hover .feature-icon {
    transform: scale(1.1);
}

.feature-box h4 {
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

/* Icon Box */
.icon-box {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

/* Contact Icon */
.contact-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

/* Course Card */
.course-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.course-img {
    position: relative;
    overflow: hidden;
}

.course-img img {
    transition: var(--transition);
    width: 100%;
}

.course-card:hover .course-img img {
    transform: scale(1.1);
}

.course-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.course-content {
    padding: 20px;
}

.course-content h5 {
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.course-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--gray-color);
}

/* Testimonial Card */
.testimonial-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    position: relative;
    transition: var(--transition);
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    border: 5px solid rgba(67, 97, 238, 0.1);
}

.testimonial-content {
    text-align: center;
}

.testimonial-rating {
    color: #f9c74f;
    margin-bottom: 15px;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.testimonial-content h5 {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.testimonial-info {
    color: var(--gray-color);
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    margin: 0 15px;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 10px;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
}

.social-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    color: white;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    right: 20px;
    bottom: -60px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 99;
    opacity: 0;
    transition: all 0.5s ease;
}

.back-to-top.active {
    bottom: 20px;
    opacity: 1;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    color: white;
}

/* Modal fixes */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

.modal-dialog {
    margin: 1.75rem auto;
    pointer-events: auto;
}

.modal-content {
    pointer-events: auto;
}

body.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        z-index: 1030;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .content {
        width: 100%;
    }

    .navbar-toggler {
        display: block;
    }

    .course-info {
        flex-direction: column;
        gap: 5px;
    }
}

/* Responsive Styles */

/* Large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }

    .hero h1 {
        font-size: 2.8rem;
    }

    .course-card .card-title {
        font-size: 1.3rem;
    }
}

/* Medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }

    .hero {
        padding: 100px 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .dashboard-stats .card-text {
        font-size: 1.8rem;
    }

    .dashboard-stats .card-title {
        font-size: 0.9rem;
    }

    .certificate {
        padding: 30px;
    }

    .certificate h1 {
        font-size: 2.2rem;
    }

    /* Table responsiveness */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Adjust sidebar in admin/student dashboard */
    .dashboard-sidebar {
        position: static;
        width: 100%;
        margin-bottom: 20px;
    }

    .dashboard-content {
        margin-left: 0;
    }
}

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
    .hero {
        padding: 80px 0;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }

    .hero .btn-outline-light {
        margin-left: 0 !important;
    }

    .card-body {
        padding: 1.25rem;
    }

    .certificate {
        padding: 25px;
    }

    .certificate h1 {
        font-size: 2rem;
    }

    .dashboard-stats .card-text {
        font-size: 1.5rem;
    }

    .testimonial-card {
        padding: 20px;
    }

    .feature-box {
        margin-bottom: 20px;
    }

    /* Form improvements */
    .form-container {
        padding: 1.5rem;
    }

    /* Course cards */
    .course-card {
        margin-bottom: 20px;
    }

    /* Tables */
    .table-responsive table {
        min-width: 650px;
    }

    /* Fix spacing issues */
    .section-title {
        margin-bottom: 25px;
    }

    /* Course list container */
    .courses-table-container {
        height: auto;
        max-height: 300px;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding-left: 20px;
        padding-right: 20px;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .section-title h2 {
        font-size: 1.5rem;
    }

    .profile-photo {
        width: 120px;
        height: 120px;
    }

    .form-container {
        padding: 1.25rem;
    }

    .cta-section {
        text-align: center;
    }

    .cta-section .btn {
        margin-top: 15px;
        display: block;
        width: 100%;
    }

    .testimonial-img {
        width: 60px;
        height: 60px;
    }

    /* Card adjustments */
    .card-title {
        font-size: 1.2rem;
    }

    /* Form elements */
    .form-label {
        font-size: 0.9rem;
    }

    /* Buttons */
    .btn {
        padding: 0.5rem 1.2rem;
    }

    /* Footer */
    footer {
        text-align: center;
    }

    footer .text-end {
        text-align: center !important;
        margin-top: 15px;
    }

    /* Certificate verification */
    .certificate-verification {
        padding: 15px;
    }

    /* Login forms */
    .login-form {
        padding: 20px;
    }

    /* Navigation spacing */
    .navbar-brand {
        font-size: 1.3rem;
    }
}

/* Very small devices */
@media (max-width: 359.98px) {
    .hero h1 {
        font-size: 1.5rem;
    }

    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}
