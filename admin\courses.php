<?php
$page_title = "Manage Courses";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Process course actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new course
    if (isset($_POST['add_course'])) {
        $course_name = clean($_POST['course_name']);
        $duration = clean($_POST['duration']);
        $fee = (float) $_POST['fee'];
        $description = clean($_POST['description']);
        $status = clean($_POST['status']);

        $sql = "INSERT INTO courses (course_name, duration, fee, description, status) VALUES (?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssdss", $course_name, $duration, $fee, $description, $status);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Course added successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error adding course: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Update course
    if (isset($_POST['update_course'])) {
        $course_id = (int) $_POST['course_id'];
        $course_name = clean($_POST['course_name']);
        $duration = clean($_POST['duration']);
        $fee = (float) $_POST['fee'];
        $description = clean($_POST['description']);
        $status = clean($_POST['status']);

        $sql = "UPDATE courses SET course_name = ?, duration = ?, fee = ?, description = ?, status = ? WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssdssi", $course_name, $duration, $fee, $description, $status, $course_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Course updated successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating course: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Delete course
    if (isset($_POST['delete_course'])) {
        $course_id = (int) $_POST['course_id'];

        // Check if course is assigned to any student
        $check_sql = "SELECT COUNT(*) as count FROM students WHERE course_id = ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "i", $course_id);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);
        $check_row = mysqli_fetch_assoc($check_result);

        if ($check_row['count'] > 0) {
            $_SESSION['message'] = 'Cannot delete course. It is assigned to ' . $check_row['count'] . ' student(s).';
            $_SESSION['message_type'] = 'danger';
        } else {
            $sql = "DELETE FROM courses WHERE id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $course_id);

            if (mysqli_stmt_execute($stmt)) {
                $_SESSION['message'] = 'Course deleted successfully';
                $_SESSION['message_type'] = 'success';
            } else {
                $_SESSION['message'] = 'Error deleting course: ' . mysqli_error($conn);
                $_SESSION['message_type'] = 'danger';
            }
        }
    }

    // Redirect to avoid form resubmission
    redirect(SITE_URL . 'admin/courses.php');
}

// Get course for editing
$edit_course = null;
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
    $course_id = (int) $_GET['id'];
    $result = mysqli_query($conn, "SELECT * FROM courses WHERE id = $course_id");
    $edit_course = mysqli_fetch_assoc($result);
}

// Get all courses
$result = mysqli_query($conn, "SELECT * FROM courses ORDER BY course_name");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Manage Courses</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-primary modal-button" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                            <i class="fas fa-plus"></i> Add New Course
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Courses Table -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Course Name</th>
                                        <th>Duration</th>
                                        <th>Fee (Rs.)</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($result) > 0): ?>
                                        <?php $i = 1; while ($course = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td><?php echo $course['course_name']; ?></td>
                                                <td><?php echo $course['duration']; ?></td>
                                                <td><?php echo number_format($course['fee'], 2); ?></td>
                                                <td>
                                                    <?php if ($course['status'] == 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="?action=edit&id=<?php echo $course['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger modal-button" data-bs-toggle="modal" data-bs-target="#deleteCourseModal<?php echo $course['id']; ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>

                                                    <!-- Delete Course Modal -->
                                                    <div class="modal fade" id="deleteCourseModal<?php echo $course['id']; ?>" tabindex="-1" aria-labelledby="deleteCourseModalLabel<?php echo $course['id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog" onclick="event.stopPropagation();">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="deleteCourseModalLabel<?php echo $course['id']; ?>">Confirm Delete</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <p>Are you sure you want to delete the course: <strong><?php echo $course['course_name']; ?></strong>?</p>
                                                                    <p class="text-danger">This action cannot be undone.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary modal-cancel-btn" data-bs-dismiss="modal">Cancel</button>
                                                                    <form method="post" action="" class="modal-form">
                                                                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                                        <button type="submit" name="delete_course" class="btn btn-danger">Delete</button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No courses found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Course Modal -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" aria-labelledby="addCourseModalLabel" aria-hidden="true">
        <div class="modal-dialog" onclick="event.stopPropagation();">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCourseModalLabel">Add New Course</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="" class="modal-form">
                        <div class="mb-3">
                            <label for="course_name" class="form-label">Course Name</label>
                            <input type="text" class="form-control" id="course_name" name="course_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="duration" class="form-label">Duration</label>
                            <input type="text" class="form-control" id="duration" name="duration" placeholder="e.g., 3 Months" required>
                        </div>
                        <div class="mb-3">
                            <label for="fee" class="form-label">Fee (Rs.)</label>
                            <input type="number" class="form-control" id="fee" name="fee" min="0" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary modal-cancel-btn" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="add_course" class="btn btn-primary">Add Course</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Course Modal -->
    <?php if ($edit_course): ?>
        <div class="modal fade" id="editCourseModal" tabindex="-1" aria-labelledby="editCourseModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog" onclick="event.stopPropagation();">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editCourseModalLabel">Edit Course</h5>
                        <a href="courses.php" class="btn-close"></a>
                    </div>
                    <div class="modal-body">
                        <form method="post" action="" class="modal-form">
                            <input type="hidden" name="course_id" value="<?php echo $edit_course['id']; ?>">
                            <div class="mb-3">
                                <label for="edit_course_name" class="form-label">Course Name</label>
                                <input type="text" class="form-control" id="edit_course_name" name="course_name" value="<?php echo $edit_course['course_name']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_duration" class="form-label">Duration</label>
                                <input type="text" class="form-control" id="edit_duration" name="duration" value="<?php echo $edit_course['duration']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_fee" class="form-label">Fee (Rs.)</label>
                                <input type="number" class="form-control" id="edit_fee" name="fee" min="0" step="0.01" value="<?php echo $edit_course['fee']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_description" class="form-label">Description</label>
                                <textarea class="form-control" id="edit_description" name="description" rows="3"><?php echo $edit_course['description']; ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status</label>
                                <select class="form-select" id="edit_status" name="status" required>
                                    <option value="active" <?php echo $edit_course['status'] == 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $edit_course['status'] == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="text-end">
                                <a href="courses.php" class="btn btn-secondary modal-cancel-btn">Cancel</a>
                                <button type="submit" name="update_course" class="btn btn-primary">Update Course</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var editCourseModal = new bootstrap.Modal(document.getElementById('editCourseModal'));
                editCourseModal.show();
            });
        </script>
    <?php endif; ?>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Modal Fix JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/modal-fix.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>


</body>
</html>
