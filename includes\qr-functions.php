<?php
/**
 * Simple QR Code Generator Functions
 *
 * This file contains functions to generate QR codes without external libraries
 * by using Google Chart API
 */

/**
 * Generate QR code image URL using QR Server API
 *
 * @param string $data The data to encode in the QR code
 * @param int $size The size of the QR code in pixels
 * @param string $color The color of the QR code (without #)
 * @return string The URL of the QR code image
 */
function generateQRCodeUrl($data, $size = 150, $color = '000000') {
    // URL encode the data
    $encodedData = urlencode($data);

    // Generate the QR Server API URL
    $url = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$encodedData}&color={$color}";

    return $url;
}

/**
 * Generate QR code HTML img tag
 *
 * @param string $data The data to encode in the QR code
 * @param int $size The size of the QR code in pixels
 * @param string $color The color of the QR code (without #)
 * @param string $alt Alt text for the image
 * @param string $class CSS class for the image
 * @return string HTML img tag for the QR code
 */
function generateQRCodeImage($data, $size = 150, $color = '000000', $alt = 'QR Code', $class = '') {
    $url = generateQRCodeUrl($data, $size, $color);

    $classAttr = $class ? " class=\"{$class}\"" : '';

    return "<img src=\"{$url}\" alt=\"{$alt}\" width=\"{$size}\" height=\"{$size}\"{$classAttr}>";
}

/**
 * Generate QR code for certificate verification
 *
 * @param string $certificateNumber The certificate number
 * @param int $size The size of the QR code in pixels
 * @return string HTML img tag for the certificate verification QR code
 */
function generateCertificateVerificationQR($certificateNumber, $size = 150) {
    // Create verification URL
    $verificationUrl = SITE_URL . "verify.php?cert=" . $certificateNumber;

    // Generate QR code
    return generateQRCodeImage($verificationUrl, $size, '4361ee', 'Scan to verify certificate', 'qr-code');
}
?>
