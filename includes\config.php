<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'cti_db');

// Application configuration
define('SITE_NAME', 'City Technical Institute Marot');

// Automatically detect the base URL
$base_url = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https://' : 'http://');
$base_url .= $_SERVER['HTTP_HOST'];
$base_url .= str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
$base_url = rtrim(dirname($base_url), '/') . '/';

define('SITE_URL', $base_url);

// Define upload paths
$doc_root = str_replace('\\', '/', $_SERVER['DOCUMENT_ROOT']);
$site_path = str_replace('\\', '/', dirname(__FILE__));
$relative_path = str_replace($doc_root, '', $site_path);
$relative_path = str_replace('/includes', '', $relative_path);
$upload_path = $doc_root . $relative_path . '/assets/uploads/';

define('UPLOAD_PATH', $upload_path);
define('PROFILE_PATH', UPLOAD_PATH . 'profile_photos/');
define('CERTIFICATE_PATH', UPLOAD_PATH . 'certificates/');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database setup file (run this manually when needed)
// require_once 'setup.php';

?>
