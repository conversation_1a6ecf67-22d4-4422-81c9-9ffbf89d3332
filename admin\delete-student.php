<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['message'] = 'Invalid student ID';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student_id = (int) $_GET['id'];
$conn = getDbConnection();

// Check if confirmation is provided
if (isset($_GET['confirm']) && $_GET['confirm'] == 'yes') {
    // Begin transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Delete related records first
        
        // Delete certificates
        $sql = "DELETE FROM certificates WHERE student_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        
        // Delete fees
        $sql = "DELETE FROM fees WHERE student_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        
        // Delete course enrollments
        $sql = "DELETE FROM student_courses WHERE student_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        
        // Get user ID associated with student
        $sql = "SELECT user_id FROM students WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user_id = null;
        
        if (mysqli_num_rows($result) > 0) {
            $user_id = mysqli_fetch_assoc($result)['user_id'];
        }
        
        // Delete student
        $sql = "DELETE FROM students WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        
        // Delete user account if exists
        if ($user_id) {
            $sql = "DELETE FROM users WHERE id = ? AND role = 'student'";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        $_SESSION['message'] = 'Student deleted successfully';
        $_SESSION['message_type'] = 'success';
        redirect(SITE_URL . 'admin/students.php');
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        
        $_SESSION['message'] = 'Error deleting student: ' . $e->getMessage();
        $_SESSION['message_type'] = 'danger';
        redirect(SITE_URL . 'admin/student-details.php?id=' . $student_id);
    }
} else {
    // Get student details for confirmation
    $sql = "SELECT * FROM students WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $student_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) == 0) {
        $_SESSION['message'] = 'Student not found';
        $_SESSION['message_type'] = 'danger';
        redirect(SITE_URL . 'admin/students.php');
    }
    
    $student = mysqli_fetch_assoc($result);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Student - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Poppins', sans-serif;
        }
        .confirmation-container {
            max-width: 600px;
            margin: 100px auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .confirmation-header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .confirmation-content {
            padding: 30px;
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning-text {
            color: #e74c3c;
            font-weight: 600;
        }
        .btn-delete {
            background-color: #e74c3c;
            color: white;
            border: none;
        }
        .btn-delete:hover {
            background-color: #c0392b;
            color: white;
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="confirmation-header">
            <h1><i class="fas fa-exclamation-triangle me-2"></i> Delete Student</h1>
        </div>
        
        <div class="confirmation-content">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i> Warning!</h5>
                <p>You are about to delete a student record. This action cannot be undone.</p>
                <p class="mb-0">All related data including certificates, fees, and course enrollments will also be deleted.</p>
            </div>
            
            <div class="student-info">
                <h5>Student Information</h5>
                <p><strong>Name:</strong> <?php echo $student['full_name']; ?></p>
                <p><strong>Father's Name:</strong> <?php echo $student['father_name']; ?></p>
                <p><strong>CNIC:</strong> <?php echo $student['cnic']; ?></p>
                <p class="mb-0"><strong>Registration Date:</strong> <?php echo formatDate($student['registration_date']); ?></p>
            </div>
            
            <p class="warning-text">Are you sure you want to delete this student?</p>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="student-details.php?id=<?php echo $student_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Cancel
                </a>
                <a href="delete-student.php?id=<?php echo $student_id; ?>&confirm=yes" class="btn btn-delete">
                    <i class="fas fa-trash me-2"></i> Yes, Delete Student
                </a>
            </div>
        </div>
    </div>
</body>
</html>
