<?php
$page_title = "Student Dashboard";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/logout.php');
}

$conn = getDbConnection();

// Get student's courses
$courses_query = "SELECT c.*, sc.status as enrollment_status, sc.enrollment_date
                 FROM student_courses sc
                 JOIN courses c ON sc.course_id = c.id
                 WHERE sc.student_id = ?";
$stmt = mysqli_prepare($conn, $courses_query);
mysqli_stmt_bind_param($stmt, "i", $student['id']);
mysqli_stmt_execute($stmt);
$courses_result = mysqli_stmt_get_result($stmt);

// If no courses found, use the main course from students table for backward compatibility
if (mysqli_num_rows($courses_result) == 0) {
    // Get course details
    $course_query = "SELECT * FROM courses WHERE id = ?";
    $stmt = mysqli_prepare($conn, $course_query);
    mysqli_stmt_bind_param($stmt, "i", $student['course_id']);
    mysqli_stmt_execute($stmt);
    $course_result = mysqli_stmt_get_result($stmt);
    $course = mysqli_fetch_assoc($course_result);

    // Add to student_courses table for future compatibility
    if ($course) {
        $enrollment_date = $student['registration_date'];
        $status = $student['course_status'];

        $insert_query = "INSERT INTO student_courses (student_id, course_id, enrollment_date, status)
                         VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, "iiss", $student['id'], $student['course_id'], $enrollment_date, $status);
        mysqli_stmt_execute($stmt);

        // Refresh courses result
        mysqli_stmt_execute($stmt);
        $courses_result = mysqli_stmt_get_result($stmt);
    }
}

// Get fee status
$fee = getFeeStatus($student['id']);

// Check if student has any certificates
$cert_query = "SELECT COUNT(*) as cert_count FROM certificates WHERE student_id = ?";
$stmt = mysqli_prepare($conn, $cert_query);
mysqli_stmt_bind_param($stmt, "i", $student['id']);
mysqli_stmt_execute($stmt);
$cert_result = mysqli_stmt_get_result($stmt);
$cert_count = mysqli_fetch_assoc($cert_result)['cert_count'];
$has_certificates = ($cert_count > 0);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Student Portal</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enroll-course.php">
                                <i class="fas fa-book"></i> Enroll in Course
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> My Certificates
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Student Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-home"></i> Visit Website
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Welcome Message -->
                <div class="alert alert-info mb-4">
                    <h4>Welcome, <?php echo $student['full_name']; ?>!</h4>
                    <p>This is your student dashboard where you can view your profile, course details, fee status, and download your certificate (if eligible).</p>
                </div>

                <!-- Student Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Personal Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 text-center mb-3">
                                        <?php if ($student['profile_photo']): ?>
                                            <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="img-thumbnail profile-photo">
                                        <?php else: ?>
                                            <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="img-thumbnail profile-photo">
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-8">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="40%">Name:</th>
                                                <td><?php echo $student['full_name']; ?></td>
                                            </tr>
                                            <tr>
                                                <th>CNIC:</th>
                                                <td><?php echo $student['cnic']; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Father's Name:</th>
                                                <td><?php echo $student['father_name']; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Mobile:</th>
                                                <td><?php echo $student['mobile']; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Registration Date:</th>
                                                <td><?php echo formatDate($student['registration_date']); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Course Information</h5>
                            </div>
                            <div class="card-body">
                                <?php if (mysqli_num_rows($courses_result) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Course</th>
                                                    <th>Duration</th>
                                                    <th>Fee</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($course = mysqli_fetch_assoc($courses_result)): ?>
                                                    <tr>
                                                        <td><?php echo $course['course_name']; ?></td>
                                                        <td><?php echo $course['duration']; ?></td>
                                                        <td>Rs. <?php echo number_format($course['fee'], 2); ?></td>
                                                        <td>
                                                            <?php if ($course['enrollment_status'] == 'completed'): ?>
                                                                <span class="badge bg-success">Completed</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-primary">Ongoing</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <p class="mb-0">No courses found. Please enroll in a course.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="profile.php" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-user mb-2 d-block fs-4"></i>
                                    View Full Profile
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="print-admission-form.php" class="btn btn-outline-success w-100 py-3" target="_blank">
                                    <i class="fas fa-download mb-2 d-block fs-4"></i>
                                    Download Admission Form
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="enroll-course.php" class="btn btn-outline-warning w-100 py-3">
                                    <i class="fas fa-book mb-2 d-block fs-4"></i>
                                    Enroll in New Course
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="certificates.php" class="btn btn-outline-info w-100 py-3">
                                    <i class="fas fa-certificate mb-2 d-block fs-4"></i>
                                    View Certificates
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Certificate Status -->
                <?php
                // Get all student courses
                $courses_query = "SELECT sc.*, c.course_name, c.duration, c.fee
                                 FROM student_courses sc
                                 JOIN courses c ON sc.course_id = c.id
                                 WHERE sc.student_id = ?
                                 ORDER BY sc.enrollment_date DESC";
                $stmt = mysqli_prepare($conn, $courses_query);
                mysqli_stmt_bind_param($stmt, "i", $student['id']);
                mysqli_stmt_execute($stmt);
                $courses_result = mysqli_stmt_get_result($stmt);

                // Get all certificates
                $certificates = getAllStudentCertificates($student['id']);
                $has_certificates = !empty($certificates);

                // Count completed courses
                $completed_courses_query = "SELECT COUNT(*) as count FROM student_courses WHERE student_id = ? AND status = 'completed'";
                $stmt = mysqli_prepare($conn, $completed_courses_query);
                mysqli_stmt_bind_param($stmt, "i", $student['id']);
                mysqli_stmt_execute($stmt);
                $completed_result = mysqli_stmt_get_result($stmt);
                $completed_count = mysqli_fetch_assoc($completed_result)['count'];

                // Count certificates
                $cert_count = count($certificates);

                if ($has_certificates):
                ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i> Congratulations!</h5>
                        <p>You have successfully completed <?php echo $cert_count > 1 ? $cert_count . ' courses' : 'a course'; ?> and your <?php echo $cert_count > 1 ? 'certificates are' : 'certificate is'; ?> ready. You can view and download <?php echo $cert_count > 1 ? 'them' : 'it'; ?> from the <a href="certificates.php" class="alert-link">Certificates</a> page.</p>
                    </div>
                <?php endif; ?>

                <?php if ($completed_count > $cert_count): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i> Certificate Processing</h5>
                        <p>You have completed additional courses. Your certificates are being processed and will be available soon. Please ensure all course fees are paid.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>
</body>
</html>
