/**
 * Image optimization script for CTI website
 * This script converts regular images to lazy-loaded images
 */

document.addEventListener('DOMContentLoaded', function() {
    // Convert all images to lazy loading
    convertImagesToLazyLoad();
    
    // Set up intersection observer for lazy loading
    setupLazyLoadObserver();
});

/**
 * Convert regular images to lazy loading format
 */
function convertImagesToLazyLoad() {
    // Get all images except those already set up for lazy loading or with lazy class
    const images = document.querySelectorAll('img:not([loading="lazy"]):not(.lazy):not(.no-lazy)');
    
    images.forEach(function(img) {
        // Skip images without src
        if (!img.src) return;
        
        // Add loading="lazy" attribute for native lazy loading
        img.setAttribute('loading', 'lazy');
        
        // For older browsers, use data-src pattern
        if (!('loading' in HTMLImageElement.prototype)) {
            // Store original src in data-src
            img.setAttribute('data-src', img.src);
            
            // Set placeholder or very small image
            if (img.hasAttribute('data-placeholder')) {
                img.src = img.getAttribute('data-placeholder');
            } else {
                // Default placeholder - transparent pixel
                img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';
            }
            
            // Add lazy class for custom styling
            img.classList.add('lazy');
        }
    });
}

/**
 * Set up intersection observer for lazy loading images
 */
function setupLazyLoadObserver() {
    // Skip if browser supports native lazy loading
    if ('loading' in HTMLImageElement.prototype) return;
    
    // Get all images with lazy class
    const lazyImages = document.querySelectorAll('img.lazy');
    
    // Set up intersection observer if available
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    
                    // Replace src with data-src
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                    }
                    
                    // Replace srcset with data-srcset if available
                    if (img.dataset.srcset) {
                        img.srcset = img.dataset.srcset;
                    }
                    
                    // Remove lazy class
                    img.classList.remove('lazy');
                    
                    // Stop observing this image
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '200px 0px', // Start loading 200px before image enters viewport
            threshold: 0.01 // Trigger when at least 1% of the image is visible
        });
        
        // Observe all lazy images
        lazyImages.forEach(function(img) {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        let lazyLoadThrottleTimeout;
        
        function lazyLoad() {
            if (lazyLoadThrottleTimeout) {
                clearTimeout(lazyLoadThrottleTimeout);
            }
            
            lazyLoadThrottleTimeout = setTimeout(function() {
                const scrollTop = window.pageYOffset;
                
                lazyImages.forEach(function(img) {
                    if (img.offsetTop < (window.innerHeight + scrollTop + 200)) {
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                        }
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                        }
                        img.classList.remove('lazy');
                    }
                });
                
                if (lazyImages.length == 0) {
                    document.removeEventListener('scroll', lazyLoad);
                    window.removeEventListener('resize', lazyLoad);
                    window.removeEventListener('orientationChange', lazyLoad);
                }
            }, 20);
        }
        
        // Add event listeners for fallback lazy loading
        document.addEventListener('scroll', lazyLoad);
        window.addEventListener('resize', lazyLoad);
        window.addEventListener('orientationChange', lazyLoad);
        
        // Initial load
        lazyLoad();
    }
}

/**
 * Add CSS for lazy images
 */
function addLazyImageStyles() {
    const style = document.createElement('style');
    style.textContent = `
        img.lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }
        img.lazy.loaded {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}
