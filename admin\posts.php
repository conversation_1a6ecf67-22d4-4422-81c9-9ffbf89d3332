<?php
$page_title = "Manage Posts";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Process post actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new post
    if (isset($_POST['add_post'])) {
        $title = clean($_POST['title']);
        $content = clean($_POST['content']);
        $status = clean($_POST['status']);

        $sql = "INSERT INTO posts (title, content, status) VALUES (?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sss", $title, $content, $status);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Post added successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error adding post: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Update post
    if (isset($_POST['update_post'])) {
        $post_id = (int) $_POST['post_id'];
        $title = clean($_POST['title']);
        $content = clean($_POST['content']);
        $status = clean($_POST['status']);

        $sql = "UPDATE posts SET title = ?, content = ?, status = ? WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sssi", $title, $content, $status, $post_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Post updated successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating post: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Delete post
    if (isset($_POST['delete_post'])) {
        $post_id = (int) $_POST['post_id'];

        $sql = "DELETE FROM posts WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $post_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Post deleted successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error deleting post: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to avoid form resubmission
    redirect(SITE_URL . 'admin/posts.php');
}

// Get post for editing
$edit_post = null;
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
    $post_id = (int) $_GET['id'];
    $result = mysqli_query($conn, "SELECT * FROM posts WHERE id = $post_id");
    $edit_post = mysqli_fetch_assoc($result);
}

// Get all posts
$result = mysqli_query($conn, "SELECT * FROM posts ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Manage Posts</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-primary modal-button" data-bs-toggle="modal" data-bs-target="#addPostModal">
                            <i class="fas fa-plus"></i> Add New Post
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Posts Table -->
                <div class="card shadow-sm mb-4" data-aos="fade-up">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($result) > 0): ?>
                                        <?php $i = 1; while ($post = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td><?php echo $post['title']; ?></td>
                                                <td>
                                                    <?php if ($post['status'] == 'published'): ?>
                                                        <span class="badge bg-success">Published</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Draft</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d M Y', strtotime($post['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="?action=edit&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-danger modal-button" data-bs-toggle="modal" data-bs-target="#deletePostModal<?php echo $post['id']; ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>

                                                        <!-- Delete Post Modal -->
                                                        <div class="modal fade" id="deletePostModal<?php echo $post['id']; ?>" tabindex="-1" aria-hidden="true">
                                                            <div class="modal-dialog" onclick="event.stopPropagation();">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title">Delete Post</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <p>Are you sure you want to delete the post: <strong><?php echo $post['title']; ?></strong>?</p>
                                                                        <p class="text-danger">This action cannot be undone.</p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary modal-cancel-btn" data-bs-dismiss="modal">Cancel</button>
                                                                        <form method="post" action="" class="modal-form">
                                                                            <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                                                                            <button type="submit" name="delete_post" class="btn btn-danger">Delete</button>
                                                                        </form>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="py-5">
                                                    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                                                    <h5>No posts found</h5>
                                                    <p class="text-muted">No posts have been created yet.</p>
                                                    <button type="button" class="btn btn-primary mt-3 modal-button" data-bs-toggle="modal" data-bs-target="#addPostModal">
                                                        <i class="fas fa-plus me-2"></i> Add New Post
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Post Modal -->
    <div class="modal fade" id="addPostModal" tabindex="-1" aria-labelledby="addPostModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" onclick="event.stopPropagation();">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPostModalLabel">Add New Post</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="" class="modal-form">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">Content</label>
                            <textarea class="form-control" id="content" name="content" rows="6" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="published">Published</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary modal-cancel-btn" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="add_post" class="btn btn-primary">Add Post</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Post Modal -->
    <?php if ($edit_post): ?>
        <div class="modal fade" id="editPostModal" tabindex="-1" aria-labelledby="editPostModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-lg" onclick="event.stopPropagation();">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editPostModalLabel">Edit Post</h5>
                        <a href="posts.php" class="btn-close"></a>
                    </div>
                    <div class="modal-body">
                        <form method="post" action="" class="modal-form">
                            <input type="hidden" name="post_id" value="<?php echo $edit_post['id']; ?>">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Title</label>
                                <input type="text" class="form-control" id="edit_title" name="title" value="<?php echo $edit_post['title']; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_content" class="form-label">Content</label>
                                <textarea class="form-control" id="edit_content" name="content" rows="6" required><?php echo $edit_post['content']; ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status</label>
                                <select class="form-select" id="edit_status" name="status" required>
                                    <option value="published" <?php echo $edit_post['status'] == 'published' ? 'selected' : ''; ?>>Published</option>
                                    <option value="draft" <?php echo $edit_post['status'] == 'draft' ? 'selected' : ''; ?>>Draft</option>
                                </select>
                            </div>
                            <div class="text-end">
                                <a href="posts.php" class="btn btn-secondary modal-cancel-btn">Cancel</a>
                                <button type="submit" name="update_post" class="btn btn-primary">Update Post</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var editPostModal = new bootstrap.Modal(document.getElementById('editPostModal'));
                editPostModal.show();
            });
        </script>
    <?php endif; ?>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- Modal Fix JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/modal-fix.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>

    <script>
        // Initialize AOS animation
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.navbar-toggler');
        const sidebar = document.querySelector('.sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Close sidebar when clicking outside
            document.addEventListener('click', function(e) {
                if (sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }
    </script>
</body>
</html>
