<?php
$page_title = "Student Enrollments";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Get all students with their enrollments count
$query = "SELECT s.id, s.full_name, s.cnic, s.status, s.profile_photo, 
          COUNT(sc.id) as enrollment_count
          FROM students s
          LEFT JOIN student_courses sc ON s.id = sc.student_id
          GROUP BY s.id
          ORDER BY s.created_at DESC";

$result = mysqli_query($conn, $query);

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Apply filter if needed
if ($status_filter) {
    $query = "SELECT s.id, s.full_name, s.cnic, s.status, s.profile_photo, 
              COUNT(sc.id) as enrollment_count
              FROM students s
              LEFT JOIN student_courses sc ON s.id = sc.student_id
              WHERE s.status = '" . mysqli_real_escape_string($conn, $status_filter) . "'
              GROUP BY s.id
              ORDER BY s.created_at DESC";
    
    $result = mysqli_query($conn, $query);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #2c3e50);
            color: white;
            min-height: 100vh;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.3rem 0.5rem;
            border-radius: 0.5rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, #4361ee, #7209b7);
        }
        .sidebar .nav-link i {
            margin-right: 0.8rem;
        }
        .content {
            padding: 20px;
        }
        .student-photo {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 50%;
        }
        .filter-bar {
            background-color: white;
            border-radius: 0.5rem;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .enrollment-badge {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
            border-radius: 30px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="student-enrollments.php">
                                <i class="fas fa-book"></i> Enrollments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">Student Enrollments</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Enrollments</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-home"></i> Visit Website
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Filter Bar -->
                <div class="filter-bar">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="student-enrollments.php" class="btn <?php echo $status_filter == '' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    All Students
                                </a>
                                <a href="student-enrollments.php?status=pending" class="btn <?php echo $status_filter == 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Pending
                                </a>
                                <a href="student-enrollments.php?status=approved" class="btn <?php echo $status_filter == 'approved' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Approved
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search students...">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="studentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Photo</th>
                                        <th>Name</th>
                                        <th>CNIC</th>
                                        <th>Status</th>
                                        <th>Enrollments</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($result) > 0): ?>
                                        <?php $i = 1; while ($student = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td>
                                                    <?php if ($student['profile_photo']): ?>
                                                        <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="student-photo">
                                                    <?php else: ?>
                                                        <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="student-photo">
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $student['full_name']; ?></td>
                                                <td><?php echo $student['cnic']; ?></td>
                                                <td>
                                                    <?php if ($student['status'] == 'pending'): ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php elseif ($student['status'] == 'approved'): ?>
                                                        <span class="badge bg-success">Approved</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Rejected</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="enrollment-badge bg-<?php echo $student['enrollment_count'] > 1 ? 'success' : 'primary'; ?>">
                                                        <?php echo $student['enrollment_count']; ?> Course<?php echo $student['enrollment_count'] != 1 ? 's' : ''; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="student-courses.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-book"></i> Manage Courses
                                                        </a>
                                                        <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-eye"></i> Details
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="py-5">
                                                    <i class="fas fa-user-graduate fa-4x text-muted mb-3"></i>
                                                    <h5>No students found</h5>
                                                    <?php if ($status_filter): ?>
                                                        <p class="text-muted">No students with status: <?php echo ucfirst($status_filter); ?></p>
                                                        <a href="student-enrollments.php" class="btn btn-outline-primary mt-3">View All Students</a>
                                                    <?php else: ?>
                                                        <p class="text-muted">No students have registered yet</p>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('studentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');

                if (cells.length === 0) continue;

                let found = false;

                for (let j = 2; j < 4; j++) { // Search in name and CNIC columns
                    const cellText = cells[j].textContent.toLowerCase();

                    if (cellText.indexOf(searchValue) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        });
    </script>
</body>
</html>
