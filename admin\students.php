<?php
$page_title = "Manage Students";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Process student actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' || isset($_GET['action'])) {
    $student_id = 0;

    // Get student ID from POST or GET
    if (isset($_POST['student_id'])) {
        $student_id = (int) $_POST['student_id'];
    } elseif (isset($_GET['student_id'])) {
        $student_id = (int) $_GET['student_id'];
    }

    // Approve student
    if (isset($_POST['approve_student']) || (isset($_GET['action']) && $_GET['action'] == 'approve')) {
        $sql = "UPDATE students SET status = 'approved' WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Student approved successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error approving student: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Reject student
    if (isset($_POST['reject_student']) || (isset($_GET['action']) && $_GET['action'] == 'reject')) {
        $sql = "UPDATE students SET status = 'rejected' WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['message'] = 'Student rejected successfully';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error rejecting student: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Mark course as completed
    if (isset($_POST['complete_course']) || (isset($_GET['action']) && $_GET['action'] == 'complete')) {
        $sql = "UPDATE students SET course_status = 'completed' WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $student_id);

        if (mysqli_stmt_execute($stmt)) {
            // Check if fee is paid
            $fee_result = mysqli_query($conn, "SELECT status FROM fees WHERE student_id = $student_id");
            $fee_status = mysqli_fetch_assoc($fee_result)['status'];

            if ($fee_status == 'paid') {
                // Generate certificate
                $student_result = mysqli_query($conn, "SELECT course_id FROM students WHERE id = $student_id");
                $course_id = mysqli_fetch_assoc($student_result)['course_id'];

                // Generate unique certificate number
                $certificate_number = generateCertificateNumber();

                // Insert certificate record
                $cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
                            VALUES (?, ?, CURDATE(), ?)";
                $cert_stmt = mysqli_prepare($conn, $cert_sql);
                mysqli_stmt_bind_param($cert_stmt, "isi", $student_id, $certificate_number, $course_id);
                mysqli_stmt_execute($cert_stmt);

                $_SESSION['message'] = 'Course marked as completed and certificate generated successfully';
            } else {
                $_SESSION['message'] = 'Course marked as completed. Certificate will be generated after fee payment';
            }

            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Error updating course status: ' . mysqli_error($conn);
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to avoid form resubmission
    redirect(SITE_URL . 'admin/students.php' . (isset($_GET['status']) ? '?status=' . $_GET['status'] : ''));
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filters
$query = "SELECT s.*, c.course_name, u.username
          FROM students s
          LEFT JOIN courses c ON s.course_id = c.id
          LEFT JOIN users u ON s.user_id = u.id";

if ($status_filter) {
    $query .= " WHERE s.status = '" . mysqli_real_escape_string($conn, $status_filter) . "'";
}

$query .= " ORDER BY s.created_at DESC";

// Execute query
$result = mysqli_query($conn, $query);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <!-- Modal Fix CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/modal-fix.css">
    <style>
        .sidebar {
            background: linear-gradient(180deg, var(--dark-color), #2c3e50);
            color: white;
            min-height: 100vh;
            transition: var(--transition);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.3rem 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
        }

        .content {
            padding: 20px;
        }

        @media (max-width: 992px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1030;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .content {
                width: 100%;
            }
        }

        .student-photo {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 50%;
        }

        .filter-bar {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">Manage Students</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Students</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-home"></i> Visit Website
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Filter Bar -->
                <div class="filter-bar" data-aos="fade-up">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="students.php" class="btn <?php echo $status_filter == '' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    All Students
                                </a>
                                <a href="students.php?status=pending" class="btn <?php echo $status_filter == 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Pending
                                </a>
                                <a href="students.php?status=approved" class="btn <?php echo $status_filter == 'approved' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Approved
                                </a>
                                <a href="students.php?status=rejected" class="btn <?php echo $status_filter == 'rejected' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    Rejected
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search students...">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="studentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Photo</th>
                                        <th>Name</th>
                                        <th>CNIC</th>
                                        <th>Course</th>
                                        <th>Status</th>
                                        <th>Course Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($result) > 0): ?>
                                        <?php $i = 1; while ($student = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td>
                                                    <?php if ($student['profile_photo']): ?>
                                                        <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="student-photo">
                                                    <?php else: ?>
                                                        <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="student-photo">
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $student['full_name']; ?></td>
                                                <td><?php echo $student['cnic']; ?></td>
                                                <td><?php echo $student['course_name']; ?></td>
                                                <td>
                                                    <?php if ($student['status'] == 'pending'): ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php elseif ($student['status'] == 'approved'): ?>
                                                        <span class="badge bg-success">Approved</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Rejected</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($student['course_status'] == 'completed'): ?>
                                                        <span class="badge bg-success">Completed</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">Ongoing</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        <?php if ($student['status'] == 'pending'): ?>
                                                            <!-- Approve Button (No Modal) -->
                                                            <a href="javascript:void(0)" onclick="confirmApprove(<?php echo $student['id']; ?>, '<?php echo $student['full_name']; ?>')" class="btn btn-sm btn-success">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                            <!-- Reject Button (No Modal) -->
                                                            <a href="javascript:void(0)" onclick="confirmReject(<?php echo $student['id']; ?>, '<?php echo $student['full_name']; ?>')" class="btn btn-sm btn-danger">
                                                                <i class="fas fa-times"></i>
                                                            </a>
                                                        <?php endif; ?>

                                                        <?php if ($student['status'] == 'approved' && $student['course_status'] == 'ongoing'): ?>
                                                            <!-- Complete Course Button (No Modal) -->
                                                            <a href="javascript:void(0)" onclick="confirmComplete(<?php echo $student['id']; ?>, '<?php echo $student['full_name']; ?>')" class="btn btn-sm btn-info">
                                                                <i class="fas fa-graduation-cap"></i>
                                                            </a>
                                                        <?php endif; ?>

                                                        <!-- Delete Student Button -->
                                                        <a href="delete-student.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>


                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="py-5">
                                                    <i class="fas fa-user-graduate fa-4x text-muted mb-3"></i>
                                                    <h5>No students found</h5>
                                                    <?php if ($status_filter): ?>
                                                        <p class="text-muted">No students with status: <?php echo ucfirst($status_filter); ?></p>
                                                        <a href="students.php" class="btn btn-outline-primary mt-3">View All Students</a>
                                                    <?php else: ?>
                                                        <p class="text-muted">No students have registered yet</p>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>

    <script>
        // Initialize AOS animation
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('studentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');

                if (cells.length === 0) continue;

                let found = false;

                for (let j = 2; j < 5; j++) { // Search in name, CNIC, and course columns
                    const cellText = cells[j].textContent.toLowerCase();

                    if (cellText.indexOf(searchValue) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.navbar-toggler');
        const sidebar = document.querySelector('.sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Close sidebar when clicking outside
            document.addEventListener('click', function(e) {
                if (sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }

        // Custom confirmation functions without modals
        function confirmApprove(studentId, studentName) {
            if (confirm('Are you sure you want to approve ' + studentName + '?')) {
                const status = new URLSearchParams(window.location.search).get('status') || '';
                const statusParam = status ? '&status=' + status : '';
                window.location.href = 'students.php?action=approve&student_id=' + studentId + statusParam;
            }
        }

        function confirmReject(studentId, studentName) {
            if (confirm('Are you sure you want to reject ' + studentName + '?')) {
                const status = new URLSearchParams(window.location.search).get('status') || '';
                const statusParam = status ? '&status=' + status : '';
                window.location.href = 'students.php?action=reject&student_id=' + studentId + statusParam;
            }
        }

        function confirmComplete(studentId, studentName) {
            if (confirm('Are you sure you want to mark the course as completed for ' + studentName + '?\nThis will generate a certificate if the fee is paid.')) {
                const status = new URLSearchParams(window.location.search).get('status') || '';
                const statusParam = status ? '&status=' + status : '';
                window.location.href = 'students.php?action=complete&student_id=' + studentId + statusParam;
            }
        }
    </script>
</body>
</html>
