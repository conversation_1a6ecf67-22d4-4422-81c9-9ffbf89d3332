<?php
$page_title = "Verify Certificate";
require_once 'includes/header.php';

$certificate = null;
$error = null;

// Process certificate verification
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $certificate_number = clean($_POST['certificate_number']);
    
    // Get certificate details
    $certificate = getCertificateByNumber($certificate_number);
    
    if (!$certificate) {
        $error = 'Certificate not found. Please check the certificate number and try again.';
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Verify Certificate</h4>
            </div>
            <div class="card-body">
                <p class="mb-4">Enter the certificate number to verify its authenticity.</p>
                
                <form method="post" action="">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" name="certificate_number" placeholder="Enter Certificate Number (e.g., CTI-2023-1234)" required>
                        <button class="btn btn-primary" type="submit">Verify</button>
                    </div>
                </form>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger mt-4">
                        <i class="fas fa-times-circle me-2"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($certificate): ?>
                    <div class="alert alert-success mt-4">
                        <i class="fas fa-check-circle me-2"></i> Certificate is valid and was issued by City Technical Institute Marot.
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Certificate Details</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Certificate Number</th>
                                    <td><?php echo $certificate['certificate_number']; ?></td>
                                </tr>
                                <tr>
                                    <th>Student Name</th>
                                    <td><?php echo $certificate['full_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>CNIC</th>
                                    <td><?php echo $certificate['cnic']; ?></td>
                                </tr>
                                <tr>
                                    <th>Father's Name</th>
                                    <td><?php echo $certificate['father_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>Course</th>
                                    <td><?php echo $certificate['course_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>Issue Date</th>
                                    <td><?php echo formatDate($certificate['issue_date']); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
