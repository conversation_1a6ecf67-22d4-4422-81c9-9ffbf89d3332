<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if certificate number is provided
if (!isset($_GET['cert'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('Certificate number is required');
}

$certificate_number = clean($_GET['cert']);

// Generate verification URL
$verification_url = SITE_URL . 'verify.php?cert=' . $certificate_number;

// Create a simple QR code using Google Chart API
$google_chart_url = 'https://chart.googleapis.com/chart?cht=qr&chs=200x200&chl=' . urlencode($verification_url) . '&chco=4361ee';

// Redirect to Google Chart API
header('Location: ' . $google_chart_url);
exit;
?>
