/* Additional responsive styles for CTI website */

/* Make tables responsive on all devices */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Responsive forms */
@media (max-width: 767.98px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-row > div {
        width: 100%;
        margin-right: 0;
    }
    
    /* Adjust form spacing */
    .form-group {
        margin-bottom: 1rem;
    }
    
    /* Make buttons full width on mobile */
    .form-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Fix file input on mobile */
    .custom-file-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Responsive admin dashboard */
@media (max-width: 991.98px) {
    .admin-dashboard .card {
        margin-bottom: 1rem;
    }
    
    /* Make sidebar collapsible on mobile */
    .sidebar-collapse {
        display: block;
    }
    
    .dashboard-sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        height: 100%;
        width: 250px;
        z-index: 1050;
        transition: all 0.3s;
        overflow-y: auto;
    }
    
    .dashboard-sidebar.active {
        left: 0;
    }
    
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }
    
    .sidebar-overlay.active {
        display: block;
    }
}

/* Responsive student dashboard */
@media (max-width: 767.98px) {
    .student-profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .student-profile-header .profile-photo {
        margin: 0 auto 1rem;
    }
    
    .student-profile-details {
        padding-left: 0;
    }
}

/* Certificate responsiveness */
@media (max-width: 767.98px) {
    .certificate {
        padding: 1.5rem;
        font-size: 0.9rem;
    }
    
    .certificate h1 {
        font-size: 1.8rem;
    }
    
    .certificate .signature {
        max-width: 120px;
    }
    
    .certificate .seal {
        max-width: 80px;
    }
}

/* Responsive course cards */
@media (max-width: 575.98px) {
    .course-card {
        margin-bottom: 1.5rem;
    }
    
    .course-card .card-body {
        padding: 1.25rem;
    }
}

/* Fix for modals on mobile */
@media (max-width: 575.98px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-content {
        border-radius: 0.25rem;
    }
    
    .modal-header {
        padding: 0.75rem 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 0.75rem 1rem;
    }
}

/* Improve navigation on small devices */
@media (max-width: 575.98px) {
    .navbar-brand {
        max-width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
    }
    
    .nav-link {
        padding: 0.5rem 0.75rem;
    }
}

/* Fix for print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    body {
        font-size: 12pt;
    }
    
    .certificate {
        border: 1px solid #ddd;
        box-shadow: none;
    }
}
