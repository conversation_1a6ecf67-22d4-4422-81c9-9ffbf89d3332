<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

// Check if student ID and course ID are provided
if (!isset($_GET['student_id']) || !is_numeric($_GET['student_id']) || !isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    $_SESSION['message'] = 'Invalid parameters';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student_id = (int) $_GET['student_id'];
$course_id = (int) $_GET['course_id'];
$conn = getDbConnection();

// Check if student exists
$student_query = "SELECT * FROM students WHERE id = ?";
$stmt = mysqli_prepare($conn, $student_query);
mysqli_stmt_bind_param($stmt, "i", $student_id);
mysqli_stmt_execute($stmt);
$student_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($student_result) == 0) {
    $_SESSION['message'] = 'Student not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student = mysqli_fetch_assoc($student_result);

// Check if course exists
$course_query = "SELECT * FROM courses WHERE id = ?";
$stmt = mysqli_prepare($conn, $course_query);
mysqli_stmt_bind_param($stmt, "i", $course_id);
mysqli_stmt_execute($stmt);
$course_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($course_result) == 0) {
    $_SESSION['message'] = 'Course not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
}

$course = mysqli_fetch_assoc($course_result);

// Check if student is enrolled in this course
$enrollment_query = "SELECT * FROM student_courses WHERE student_id = ? AND course_id = ?";
$stmt = mysqli_prepare($conn, $enrollment_query);
mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);
mysqli_stmt_execute($stmt);
$enrollment_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($enrollment_result) == 0) {
    $_SESSION['message'] = 'Student is not enrolled in this course';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
}

$enrollment = mysqli_fetch_assoc($enrollment_result);

// Check if fee is paid
$fee_query = "SELECT * FROM fees WHERE student_id = ? AND course_id = ? AND status = 'paid'";
$stmt = mysqli_prepare($conn, $fee_query);
mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);
mysqli_stmt_execute($stmt);
$fee_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($fee_result) == 0) {
    $_SESSION['message'] = 'Fee is not paid for this course';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
}

// Check if certificate already exists
$cert_check_query = "SELECT * FROM certificates WHERE student_id = ? AND course_id = ?";
$stmt = mysqli_prepare($conn, $cert_check_query);
mysqli_stmt_bind_param($stmt, "ii", $student_id, $course_id);
mysqli_stmt_execute($stmt);
$cert_check_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($cert_check_result) > 0) {
    $certificate = mysqli_fetch_assoc($cert_check_result);
    $_SESSION['message'] = 'Certificate already exists for this course';
    $_SESSION['message_type'] = 'info';
    redirect(SITE_URL . 'admin/print-certificate.php?id=' . $certificate['id']);
}

// Generate certificate
$certificate_number = generateCertificateNumber();
$issue_date = date('Y-m-d');

$cert_sql = "INSERT INTO certificates (student_id, certificate_number, issue_date, course_id)
            VALUES (?, ?, ?, ?)";
$stmt = mysqli_prepare($conn, $cert_sql);
mysqli_stmt_bind_param($stmt, "issi", $student_id, $certificate_number, $issue_date, $course_id);

if (mysqli_stmt_execute($stmt)) {
    $certificate_id = mysqli_insert_id($conn);
    $_SESSION['message'] = 'Certificate generated successfully';
    $_SESSION['message_type'] = 'success';
    redirect(SITE_URL . 'admin/print-certificate.php?id=' . $certificate_id);
} else {
    $_SESSION['message'] = 'Error generating certificate: ' . mysqli_error($conn);
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/student-courses.php?id=' . $student_id);
}
?>
