# Certificate Generation Setup

This document explains how to set up the certificate generation feature for City Technical Institute Marot.

## Requirements

The certificate generation feature requires the mPDF library to generate PDF certificates. Here's how to install it:

### Installing mPDF

1. Make sure you have Composer installed on your server. If not, you can download it from [getcomposer.org](https://getcomposer.org/download/).

2. Open a terminal/command prompt and navigate to the root directory of the website (where the `composer.json` file is located).

3. Run the following command to install mPDF:

```
composer install
```

4. This will create a `vendor` directory with all the required dependencies.

## Troubleshooting

If you encounter the following error:

```
Warning: require_once(vendor/autoload.php): Failed to open stream: No such file or directory
```

It means the mPDF library is not properly installed. Please follow the installation steps above.

## Alternative Solution

If you cannot install mPDF, the system will still display certificates in the browser, but the PDF download feature will not work. In this case:

1. You can view certificates by clicking the "View" button (eye icon).
2. The "Download" button will redirect you back to the certificates page with a warning message.

## Note for Developers

The certificate generation code checks if mPDF is available before attempting to use it:

```php
$mpdf_available = false;
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    $mpdf_available = true;
}
```

This ensures the system won't crash if mPDF is not installed.
