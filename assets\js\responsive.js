/**
 * Responsive enhancement scripts for CTI website
 */

document.addEventListener('DOMContentLoaded', function() {
    // Make all tables responsive
    makeTablesResponsive();
    
    // Handle sidebar toggle for mobile
    setupSidebarToggle();
    
    // Enhance form responsiveness
    enhanceFormResponsiveness();
    
    // Fix navigation dropdown on mobile
    fixMobileNavDropdowns();
    
    // Adjust hero buttons on small screens
    adjustHeroButtons();
});

/**
 * Wrap all tables in responsive containers
 */
function makeTablesResponsive() {
    const tables = document.querySelectorAll('table:not(.responsive-exempt)');
    
    tables.forEach(table => {
        // Skip if already in a responsive container
        if (table.parentElement.classList.contains('table-responsive')) {
            return;
        }
        
        // Create responsive wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive';
        
        // Replace table with wrapped table
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });
}

/**
 * Setup sidebar toggle functionality for mobile
 */
function setupSidebarToggle() {
    const sidebarToggleBtn = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.dashboard-sidebar');
    const content = document.querySelector('.dashboard-content');
    const body = document.body;
    
    // Create overlay for mobile sidebar
    if (sidebar && !document.querySelector('.sidebar-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        body.appendChild(overlay);
        
        // Close sidebar when clicking overlay
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            body.classList.remove('sidebar-open');
        });
    }
    
    // Toggle sidebar on button click
    if (sidebarToggleBtn && sidebar) {
        const overlay = document.querySelector('.sidebar-overlay');
        
        sidebarToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('active');
            
            if (overlay) {
                overlay.classList.toggle('active');
            }
            
            body.classList.toggle('sidebar-open');
        });
    }
}

/**
 * Enhance form responsiveness
 */
function enhanceFormResponsiveness() {
    // Make file inputs show filename properly on mobile
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            let fileName = '';
            
            if (this.files && this.files.length > 0) {
                fileName = this.files[0].name;
            }
            
            // Find the label and update it
            const label = this.nextElementSibling;
            if (label && label.classList.contains('custom-file-label')) {
                if (fileName) {
                    label.textContent = fileName;
                } else {
                    label.textContent = 'Choose file';
                }
            }
        });
    });
}

/**
 * Fix dropdown navigation on mobile
 */
function fixMobileNavDropdowns() {
    // Check if we're on mobile
    const isMobile = window.innerWidth < 992;
    
    if (isMobile) {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                // If already has aria-expanded true, let it close normally
                if (this.getAttribute('aria-expanded') === 'true') {
                    return;
                }
                
                // Otherwise prevent default and manually toggle
                e.preventDefault();
                e.stopPropagation();
                
                const dropdown = this.nextElementSibling;
                
                // Close all other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    if (menu !== dropdown) {
                        menu.classList.remove('show');
                        menu.previousElementSibling.setAttribute('aria-expanded', 'false');
                    }
                });
                
                // Toggle this dropdown
                dropdown.classList.toggle('show');
                this.setAttribute('aria-expanded', dropdown.classList.contains('show'));
            });
        });
    }
}

/**
 * Adjust hero buttons on small screens
 */
function adjustHeroButtons() {
    const heroButtons = document.querySelectorAll('.hero .btn');
    const isMobile = window.innerWidth < 768;
    
    if (isMobile && heroButtons.length > 1) {
        heroButtons.forEach((btn, index) => {
            if (index > 0) {
                btn.classList.add('mt-2');
            }
        });
    }
}

/**
 * Handle window resize events
 */
window.addEventListener('resize', function() {
    // Re-run mobile-specific adjustments
    fixMobileNavDropdowns();
    adjustHeroButtons();
});
