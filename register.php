<?php
$page_title = "Student Registration";
require_once 'includes/header.php';

// Create database and tables if they don't exist
createDatabase();
createTables();

// Process registration form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $full_name = clean($_POST['full_name']);
    $cnic = clean($_POST['cnic']);
    $father_name = clean($_POST['father_name']);
    $mobile = clean($_POST['mobile']);
    $address = clean($_POST['address']);
    $course_id = (int) $_POST['course_id'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate CNIC format
    if (!validateCNIC($cnic)) {
        $_SESSION['message'] = 'Invalid CNIC format. Please use format: 12345-1234567-1';
        $_SESSION['message_type'] = 'danger';
    }
    // Check if CNIC already exists
    elseif (cnicExists($cnic)) {
        $_SESSION['message'] = 'CNIC already registered. Please login or contact admin.';
        $_SESSION['message_type'] = 'danger';
    }
    // Check if passwords match
    elseif ($password !== $confirm_password) {
        $_SESSION['message'] = 'Passwords do not match';
        $_SESSION['message_type'] = 'danger';
    }
    else {
        $conn = getDbConnection();

        // Upload profile photo if provided
        $profile_photo = null;
        if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
            $profile_photo = uploadFile($_FILES['profile_photo'], PROFILE_PATH);

            if (!$profile_photo) {
                $_SESSION['message'] = 'Error uploading profile photo. Please try again.';
                $_SESSION['message_type'] = 'danger';
                redirect(SITE_URL . 'register.php');
            }
        }

        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Create user account
        $username = strtolower(str_replace(' ', '', $full_name)) . rand(100, 999);
        $email = $username . '@ctimarot.com'; // Placeholder email

        // Start transaction
        mysqli_begin_transaction($conn);

        try {
            // Insert user
            $sql_user = "INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'student')";
            $stmt_user = mysqli_prepare($conn, $sql_user);
            mysqli_stmt_bind_param($stmt_user, "sss", $username, $hashed_password, $email);
            mysqli_stmt_execute($stmt_user);

            $user_id = mysqli_insert_id($conn);

            // Insert student
            $sql_student = "INSERT INTO students (user_id, full_name, cnic, father_name, mobile, address, course_id, profile_photo, registration_date)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURDATE())";
            $stmt_student = mysqli_prepare($conn, $sql_student);
            mysqli_stmt_bind_param($stmt_student, "isssssss", $user_id, $full_name, $cnic, $father_name, $mobile, $address, $course_id, $profile_photo);
            mysqli_stmt_execute($stmt_student);

            $student_id = mysqli_insert_id($conn);

            // Get course fee
            $sql_course = "SELECT fee FROM courses WHERE id = ?";
            $stmt_course = mysqli_prepare($conn, $sql_course);
            mysqli_stmt_bind_param($stmt_course, "i", $course_id);
            mysqli_stmt_execute($stmt_course);
            $result_course = mysqli_stmt_get_result($stmt_course);
            $course = mysqli_fetch_assoc($result_course);

            // Insert fee record
            $sql_fee = "INSERT INTO fees (student_id, amount) VALUES (?, ?)";
            $stmt_fee = mysqli_prepare($conn, $sql_fee);
            mysqli_stmt_bind_param($stmt_fee, "id", $student_id, $course['fee']);
            mysqli_stmt_execute($stmt_fee);

            // Commit transaction
            mysqli_commit($conn);

            $_SESSION['message'] = 'Registration successful! Your application is pending approval. Please note your CNIC and password for login after approval.';
            $_SESSION['message_type'] = 'success';
            redirect(SITE_URL);

        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);

            $_SESSION['message'] = 'Registration failed. Please try again later.';
            $_SESSION['message_type'] = 'danger';
        }
    }
}

// Get active courses
$courses = getActiveCourses();

// Check if course is selected from homepage
$selected_course_id = isset($_GET['course']) ? (int)$_GET['course'] : 0;
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card form-container">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">New Student Registration</h4>
            </div>
            <div class="card-body">
                <form method="post" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                            <div class="invalid-feedback">Please enter your full name</div>
                        </div>
                        <div class="col-md-6">
                            <label for="cnic" class="form-label">CNIC Number</label>
                            <input type="text" class="form-control cnic-input" id="cnic" name="cnic" placeholder="12345-1234567-1" required>
                            <div class="invalid-feedback">Please enter a valid CNIC number</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="father_name" class="form-label">Father's Name</label>
                            <input type="text" class="form-control" id="father_name" name="father_name" required>
                            <div class="invalid-feedback">Please enter your father's name</div>
                        </div>
                        <div class="col-md-6">
                            <label for="mobile" class="form-label">Mobile Number</label>
                            <input type="text" class="form-control" id="mobile" name="mobile" required>
                            <div class="invalid-feedback">Please enter your mobile number</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                        <div class="invalid-feedback">Please enter your address</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="course_id" class="form-label">Select Course</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- Select Course --</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" <?php echo ($selected_course_id == $course['id']) ? 'selected' : ''; ?>><?php echo $course['course_name']; ?> (<?php echo $course['duration']; ?>) - Rs. <?php echo number_format($course['fee'], 2); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Please select a course</div>
                        </div>
                        <div class="col-md-6">
                            <label for="profile_photo" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                            <div class="form-text">Upload a recent passport-sized photo (optional)</div>
                        </div>
                    </div>

                    <div class="mb-3 text-center">
                        <img id="profile_photo_preview" class="img-thumbnail" style="max-width: 150px; display: none;" alt="Profile Photo Preview">
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="invalid-feedback">Please enter a password</div>
                        </div>
                        <div class="col-md-6">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">Please confirm your password</div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label class="form-check-label" for="terms">I agree to the terms and conditions</label>
                        <div class="invalid-feedback">You must agree before submitting</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Register</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
