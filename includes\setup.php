<?php
// Include configuration and functions
require_once 'config.php';
require_once 'functions.php';

// Initialize database connection
$conn = getDbConnection();

// Check if database tables exist, if not create them
function setupDatabase() {
    global $conn;

    // Check if students table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'students'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create students table
        $sql = "CREATE TABLE IF NOT EXISTS `students` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `full_name` varchar(100) NOT NULL,
            `father_name` varchar(100) NOT NULL,
            `cnic` varchar(15) NOT NULL,
            `mobile` varchar(15) NOT NULL,
            `address` text NOT NULL,
            `course_id` int(11) NOT NULL,
            `registration_date` date NOT NULL,
            `profile_photo` varchar(255) DEFAULT NULL,
            `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
            `course_status` enum('ongoing','completed') NOT NULL DEFAULT 'ongoing',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `cnic` (`cnic`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);
    }

    // Check if users table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'users'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create users table
        $sql = "CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `role` enum('admin','student') NOT NULL DEFAULT 'student',
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Create default admin user
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $role = 'admin';

        $sql = "INSERT INTO `users` (`username`, `password`, `role`) VALUES (?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sss", $username, $password, $role);
        mysqli_stmt_execute($stmt);
    }

    // Check if courses table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'courses'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create courses table
        $sql = "CREATE TABLE IF NOT EXISTS `courses` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `course_name` varchar(100) NOT NULL,
            `duration` varchar(50) NOT NULL,
            `fee` decimal(10,2) NOT NULL,
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Add some default courses
        $courses = [
            ['Computer Applications', '3 Months', 5000.00],
            ['Web Development', '6 Months', 10000.00],
            ['Graphic Design', '4 Months', 8000.00],
            ['Digital Marketing', '3 Months', 7000.00]
        ];

        $sql = "INSERT INTO `courses` (`course_name`, `duration`, `fee`) VALUES (?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);

        foreach ($courses as $course) {
            mysqli_stmt_bind_param($stmt, "ssd", $course[0], $course[1], $course[2]);
            mysqli_stmt_execute($stmt);
        }
    }

    // Check if fees table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'fees'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create fees table
        $sql = "CREATE TABLE IF NOT EXISTS `fees` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL DEFAULT 1,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('paid','pending') NOT NULL DEFAULT 'pending',
            `payment_date` date DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `student_id` (`student_id`),
            CONSTRAINT `fees_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Add index for course_id
        $sql = "ALTER TABLE `fees` ADD KEY `course_id` (`course_id`)";
        mysqli_query($conn, $sql);

        // Add foreign key constraint
        $sql = "ALTER TABLE `fees` ADD CONSTRAINT `fees_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE";
        mysqli_query($conn, $sql);
    }

    // Check if certificates table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'certificates'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create certificates table
        $sql = "CREATE TABLE IF NOT EXISTS `certificates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL DEFAULT 1,
            `certificate_number` varchar(50) NOT NULL,
            `issue_date` date NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `certificate_number` (`certificate_number`),
            KEY `student_id` (`student_id`),
            CONSTRAINT `certificates_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Add index for course_id
        $sql = "ALTER TABLE `certificates` ADD KEY `course_id` (`course_id`)";
        mysqli_query($conn, $sql);

        // Add foreign key constraint
        $sql = "ALTER TABLE `certificates` ADD CONSTRAINT `certificates_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE";
        mysqli_query($conn, $sql);
    }

    // Check if student_courses table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'student_courses'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create student_courses table without foreign keys first
        $sql = "CREATE TABLE IF NOT EXISTS `student_courses` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL,
            `enrollment_date` date NOT NULL,
            `status` enum('ongoing','completed') NOT NULL DEFAULT 'ongoing',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Add indexes
        $sql = "ALTER TABLE `student_courses` ADD KEY `student_id` (`student_id`), ADD KEY `course_id` (`course_id`)";
        mysqli_query($conn, $sql);

        // Add foreign keys separately
        try {
            $sql = "ALTER TABLE `student_courses`
                   ADD CONSTRAINT `student_courses_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE";
            mysqli_query($conn, $sql);

            $sql = "ALTER TABLE `student_courses`
                   ADD CONSTRAINT `student_courses_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE";
            mysqli_query($conn, $sql);
        } catch (Exception $e) {
            // Log error but continue
            error_log("Failed to add foreign key constraints to student_courses table: " . $e->getMessage());
        }
    }

    // Check if any students exist but not in student_courses
    $sql = "SELECT COUNT(*) as count FROM students s
            WHERE NOT EXISTS (
                SELECT 1 FROM student_courses sc
                WHERE sc.student_id = s.id
            )";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);

    if ($row['count'] > 0) {
        // Migrate existing students to student_courses table
        $sql = "INSERT INTO student_courses (student_id, course_id, enrollment_date, status)
                SELECT id, course_id, registration_date, course_status
                FROM students
                WHERE NOT EXISTS (
                    SELECT 1 FROM student_courses
                    WHERE student_courses.student_id = students.id
                    AND student_courses.course_id = students.course_id
                )";

        mysqli_query($conn, $sql);
    }

    return true;
}

// Run database setup
try {
    setupDatabase();
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background-color: #f0f8ff; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1);'>";
    echo "<h1 style='color: #4361ee; text-align: center;'>Database Setup Completed Successfully!</h1>";
    echo "<p style='text-align: center; font-size: 18px;'>All database tables have been created and initialized.</p>";
    echo "<div style='text-align: center; margin-top: 30px;'>";
    echo "<a href='../index.php' style='display: inline-block; background-color: #4361ee; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>Go to Homepage</a>";
    echo "</div>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background-color: #fff0f0; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1);'>";
    echo "<h1 style='color: #e74c3c; text-align: center;'>Database Setup Error</h1>";
    echo "<p style='text-align: center; font-size: 18px;'>An error occurred during database setup:</p>";
    echo "<div style='background-color: #f9e4e4; padding: 15px; border-radius: 5px; margin: 20px 0; font-family: monospace;'>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "<p style='text-align: center;'>Please fix the error and try again.</p>";
    echo "</div>";
}
?>
