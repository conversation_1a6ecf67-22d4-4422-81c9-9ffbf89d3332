<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['message'] = 'Invalid student ID';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student_id = (int) $_GET['id'];
$conn = getDbConnection();

// Get student details
$sql = "SELECT s.*, c.course_name, c.duration, c.fee
        FROM students s
        LEFT JOIN courses c ON s.course_id = c.id
        WHERE s.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $student_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = 'Student not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/students.php');
}

$student = mysqli_fetch_assoc($result);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admission Form - <?php echo $student['full_name']; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f4f8;
            margin: 0;
            padding: 20px;
            color: #333;
            /* A4 size optimization */
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }

        .admission-form {
            width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
            /* Ensure content fits on A4 */
            box-sizing: border-box;
        }

        .admission-form::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 8px;
            background: linear-gradient(90deg, #4361ee, #7209b7);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .header::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #4361ee, #7209b7);
        }

        .header h1 {
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #2d3748;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .header p {
            font-size: 13px;
            color: #718096;
            margin-bottom: 3px;
            line-height: 1.3;
        }

        .form-title {
            text-align: center;
            margin-bottom: 15px;
            position: relative;
        }

        .form-title h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            background: linear-gradient(90deg, #4361ee, #7209b7);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            display: inline-block;
            box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
            letter-spacing: 1px;
        }

        .student-photo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            border: 3px solid white;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .info-section {
            margin-bottom: 15px;
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        }

        .info-section h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #4361ee;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 5px;
            position: relative;
        }

        .info-section h3::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: #7209b7;
        }

        .info-row {
            margin-bottom: 8px;
            padding: 4px;
            border-radius: 4px;
            font-size: 13px;
        }

        .info-label {
            font-weight: 600;
            color: #4a5568;
        }

        .signature-section {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            border-top: 1px dashed #cbd5e0;
            padding-top: 8px;
            text-align: center;
            width: 150px;
        }

        .signature-box p {
            font-weight: 500;
            color: #4a5568;
            margin-top: 3px;
            font-size: 12px;
        }

        .print-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(90deg, #4361ee, #7209b7);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .print-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
        }

        .print-button i {
            margin-right: 8px;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(0, 0, 0, 0.03);
            pointer-events: none;
            font-weight: 700;
            letter-spacing: 5px;
            white-space: nowrap;
            z-index: 0;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 0;
                width: 210mm;
                height: 297mm;
            }

            .admission-form {
                box-shadow: none;
                padding: 10mm;
                border-radius: 0;
                width: 190mm;
                height: 277mm;
                page-break-inside: avoid;
            }

            .print-button {
                display: none;
            }

            .info-section {
                box-shadow: none;
                background-color: white;
                border: 1px solid #e2e8f0;
                page-break-inside: avoid;
            }

            .watermark {
                color: rgba(0, 0, 0, 0.03);
            }

            .row {
                page-break-inside: avoid;
            }

            /* Ensure content fits on A4 */
            .info-row {
                font-size: 11pt;
            }

            .header h1 {
                font-size: 18pt;
            }

            .header p {
                font-size: 10pt;
            }

            .form-title h2 {
                font-size: 14pt;
            }

            .info-section h3 {
                font-size: 12pt;
            }
        }
    </style>
</head>
<body>
    <div class="admission-form">
        <div class="watermark">CTI MAROT</div>
        <div class="header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p>Near Aalishan School Allah Ho Town Marot</p>
            <p>Phone: 0344-7436314, 0306-4083348</p>
        </div>

        <div class="form-title">
            <h2>ADMISSION FORM</h2>
        </div>

        <div class="row mb-3">
            <div class="col-md-9">
                <div class="info-section">
                    <h3>Personal Information</h3>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Registration No:</div>
                        <div class="col-md-8">CTI-<?php echo str_pad($student['id'], 4, '0', STR_PAD_LEFT); ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Full Name:</div>
                        <div class="col-md-8"><?php echo $student['full_name']; ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Father's Name:</div>
                        <div class="col-md-8"><?php echo $student['father_name']; ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">CNIC Number:</div>
                        <div class="col-md-8"><?php echo $student['cnic']; ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Mobile Number:</div>
                        <div class="col-md-8"><?php echo $student['mobile']; ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Address:</div>
                        <div class="col-md-8"><?php echo $student['address']; ?></div>
                    </div>
                    <div class="row info-row">
                        <div class="col-md-4 info-label">Registration Date:</div>
                        <div class="col-md-8"><?php echo formatDate($student['registration_date']); ?></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <?php if (!empty($student['profile_photo'])): ?>
                    <img src="<?php echo SITE_URL . 'assets/uploads/profile_photos/' . $student['profile_photo']; ?>" alt="Profile Photo" class="student-photo" onerror="this.src='<?php echo SITE_URL; ?>assets/images/default-profile.png'">
                <?php else: ?>
                    <img src="<?php echo SITE_URL; ?>assets/images/default-profile.png" alt="Default Profile" class="student-photo">
                <?php endif; ?>
            </div>
        </div>

        <div class="info-section">
            <h3>Course Information</h3>
            <div class="row info-row">
                <div class="col-md-4 info-label">Course Name:</div>
                <div class="col-md-8"><?php echo $student['course_name']; ?></div>
            </div>
            <div class="row info-row">
                <div class="col-md-4 info-label">Duration:</div>
                <div class="col-md-8"><?php echo $student['duration']; ?></div>
            </div>
            <div class="row info-row">
                <div class="col-md-4 info-label">Course Fee:</div>
                <div class="col-md-8">Rs. <?php echo number_format($student['fee'], 2); ?></div>
            </div>
            <div class="row info-row">
                <div class="col-md-4 info-label">Status:</div>
                <div class="col-md-8">
                    <?php if ($student['status'] == 'approved'): ?>
                        <span class="text-success">Approved</span>
                    <?php elseif ($student['status'] == 'pending'): ?>
                        <span class="text-warning">Pending</span>
                    <?php else: ?>
                        <span class="text-danger">Rejected</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h3>Declaration</h3>
            <p style="font-size: 12px;">I hereby declare that the information provided above is true to the best of my knowledge. I understand that any false information may result in the cancellation of my admission.</p>
        </div>

        <div class="row signature-section">
            <div class="col-md-4">
                <div class="signature-box">
                    <p>Student's Signature</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="signature-box">
                    <p>Parent's/Guardian's Signature</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="signature-box">
                    <p>Principal's Signature</p>
                </div>
            </div>
        </div>
    </div>

    <button class="print-button" onclick="window.print()">
        <i class="fas fa-print"></i> Print Form
    </button>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
