/**
 * <PERSON><PERSON><PERSON> to create common placeholder images
 * This is a utility script that can be run once to generate common placeholder images
 */

// List of common placeholder sizes and texts
const placeholders = [
    { width: 300, height: 300, text: 'CTI Marot', filename: 'placeholder-300x300.png' },
    { width: 400, height: 250, text: 'Course Image', filename: 'placeholder-course.png' },
    { width: 400, height: 250, text: 'Coming Soon', filename: 'placeholder-coming-soon.png' },
    { width: 100, height: 100, text: 'Student', filename: 'placeholder-student.png' },
    { width: 800, height: 600, text: 'CTI Marot', filename: 'placeholder-large.png' },
    { width: 200, height: 200, text: 'Profile', filename: 'placeholder-profile.png' }
];

// Create image elements to trigger the placeholder.php script
placeholders.forEach(item => {
    const img = document.createElement('img');
    img.style.display = 'none';
    img.onload = function() {
        console.log(`Created placeholder: ${item.filename}`);
        // You could save this image using a server-side script if needed
    };
    img.src = `/cti/placeholder.php?width=${item.width}&height=${item.height}&text=${encodeURIComponent(item.text)}`;
    document.body.appendChild(img);
});

// Note: This is just a helper script. In a production environment,
// you would use a server-side script to generate and save these images.
