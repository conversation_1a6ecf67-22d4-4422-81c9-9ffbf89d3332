<?php
$page_title = "My Certificates";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/logout.php');
}

$conn = getDbConnection();

// Get all certificates for this student
$certificates = getAllStudentCertificates($student['id']);

// If no certificates, show empty message on the page instead of redirecting
$has_certificates = !empty($certificates);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
        .certificate-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .certificate-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .certificate-card .card-header {
            background: linear-gradient(135deg, #4361ee, #7209b7);
            color: white;
            font-weight: 600;
            padding: 15px;
        }
        .certificate-card .card-body {
            padding: 20px;
        }
        .certificate-info {
            margin-bottom: 15px;
        }
        .certificate-info .label {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .certificate-info .value {
            font-size: 1.1rem;
        }
        .certificate-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .btn-view-certificate {
            background: linear-gradient(135deg, #4361ee, #7209b7);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .btn-view-certificate:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Student Portal</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enroll-course.php">
                                <i class="fas fa-book"></i> Enroll in Course
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="certificates.php">
                                <i class="fas fa-certificate"></i> My Certificates
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">My Certificates</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Certificates List -->
                <?php if ($has_certificates): ?>
                    <div class="row">
                        <?php foreach ($certificates as $certificate): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card certificate-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><?php echo $certificate['course_name']; ?></h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="certificate-info">
                                            <div class="label">Certificate Number</div>
                                            <div class="value"><?php echo $certificate['certificate_number']; ?></div>
                                        </div>
                                        <div class="certificate-info">
                                            <div class="label">Course Duration</div>
                                            <div class="value"><?php echo $certificate['duration']; ?></div>
                                        </div>
                                        <div class="certificate-info">
                                            <div class="label">Issue Date</div>
                                            <div class="value"><?php echo formatDate($certificate['issue_date']); ?></div>
                                        </div>
                                        <div class="certificate-actions">
                                            <a href="certificate.php?id=<?php echo $certificate['id']; ?>" class="btn btn-view-certificate">
                                                <i class="fas fa-eye me-2"></i> View Certificate
                                            </a>
                                            <a href="certificate.php?id=<?php echo $certificate['id']; ?>&download=true" class="btn btn-outline-primary">
                                                <i class="fas fa-download me-2"></i> Download PDF
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i> No Certificates Found</h5>
                        <p>You don't have any certificates yet. Once you complete a course and your fee is paid, your certificate will be generated.</p>
                        <p>If you have completed a course and paid the fee, please contact the institute administration.</p>
                    </div>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
