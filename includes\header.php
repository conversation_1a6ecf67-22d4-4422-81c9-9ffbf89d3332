<?php
// Start output buffering
ob_start();

// Start session
session_start();

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <!-- Performance optimizations -->
    <meta name="description" content="City Technical Institute Marot - Providing quality technical education and professional training since 2010">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <!-- Preload critical assets -->
    <link rel="preload" href="<?php echo SITE_URL; ?>assets/css/style.css" as="style">
    <link rel="preload" href="<?php echo SITE_URL; ?>assets/css/responsive.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <!-- Google Fonts - Subset and display swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <!-- Critical CSS inline for faster rendering -->
    <style>
        /* Critical CSS for above-the-fold content */
        :root {
            --primary-color: #4361ee;
            --primary-dark: #3a56d4;
            --secondary-color: #7209b7;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        body {
            font-family: var(--font-family);
            line-height: 1.7;
            color: var(--dark-color);
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
        }
        .navbar {
            padding: 1rem 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.6rem;
            color: white !important;
        }
    </style>
    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" media="print" onload="this.media='all'">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/responsive.css">
    <!-- Fallback for CSS load -->
    <noscript>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    </noscript>
    <!-- Performance optimization scripts -->
    <script src="<?php echo SITE_URL; ?>assets/js/performance.js" defer></script>
    <script src="<?php echo SITE_URL; ?>assets/js/image-optimizer.js" defer></script>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                    <i class="fas fa-graduation-cap me-2"></i><?php echo SITE_NAME; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo !isset($page_title) || $page_title == 'Home' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>">
                                <i class="fas fa-home me-1"></i> Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isset($page_title) && $page_title == 'Student Registration' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>register.php">
                                <i class="fas fa-user-plus me-1"></i> Register
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isset($page_title) && $page_title == 'Verify Certificate' ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>verify-certificate.php">
                                <i class="fas fa-certificate me-1"></i> Verify Certificate
                            </a>
                        </li>
                        <?php if (isLoggedIn()): ?>
                            <?php if (isAdmin()): ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo SITE_URL; ?>admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-1"></i> Admin Panel
                                    </a>
                                </li>
                            <?php elseif (isStudent()): ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo SITE_URL; ?>student/dashboard.php">
                                        <i class="fas fa-user-graduate me-1"></i> Student Panel
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo SITE_URL; ?><?php echo isAdmin() ? 'admin' : 'student'; ?>/logout.php">
                                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="nav-item dropdown d-none d-lg-block">
                                <a class="nav-link dropdown-toggle" href="#" id="loginDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-sign-in-alt me-1"></i> Login
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="loginDropdown">
                                    <li>
                                        <a class="dropdown-item" href="<?php echo SITE_URL; ?>student/index.php">
                                            <i class="fas fa-user-graduate me-2"></i> Student Login
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo SITE_URL; ?>admin/index.php">
                                            <i class="fas fa-user-shield me-2"></i> Admin Login
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item d-lg-none">
                                <a class="nav-link" href="<?php echo SITE_URL; ?>student/index.php">
                                    <i class="fas fa-sign-in-alt me-1"></i> Student Login
                                </a>
                            </li>
                            <li class="nav-item d-lg-none">
                                <a class="nav-link" href="<?php echo SITE_URL; ?>admin/index.php">
                                    <i class="fas fa-user-shield me-1"></i> Admin Login
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main class="container py-4">
        <?php if (isset($_SESSION['message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo $_SESSION['message_type'] == 'success' ? 'check-circle' : ($_SESSION['message_type'] == 'danger' ? 'exclamation-circle' : 'info-circle'); ?> me-2"></i>
                <?php echo $_SESSION['message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
        <?php endif; ?>
