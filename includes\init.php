<?php
// This file is included by functions.php

// Initialize database connection
$conn = getDbConnection();

// Set error handling for mysqli
mysqli_report(MYSQLI_REPORT_ERROR);

// Check if database tables exist, if not create them
function initializeDatabase() {
    global $conn;

    // Check if students table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'students'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create students table
        $sql = "CREATE TABLE IF NOT EXISTS `students` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `full_name` varchar(100) NOT NULL,
            `father_name` varchar(100) NOT NULL,
            `cnic` varchar(15) NOT NULL,
            `mobile` varchar(15) NOT NULL,
            `address` text NOT NULL,
            `course_id` int(11) NOT NULL,
            `registration_date` date NOT NULL,
            `profile_photo` varchar(255) DEFAULT NULL,
            `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
            `course_status` enum('ongoing','completed') NOT NULL DEFAULT 'ongoing',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `cnic` (`cnic`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);
    }

    // Check if users table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'users'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create users table
        $sql = "CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `role` enum('admin','student') NOT NULL DEFAULT 'student',
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Create default admin user
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $role = 'admin';

        $sql = "INSERT INTO `users` (`username`, `password`, `role`) VALUES (?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "sss", $username, $password, $role);
        mysqli_stmt_execute($stmt);
    }

    // Check if courses table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'courses'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create courses table
        $sql = "CREATE TABLE IF NOT EXISTS `courses` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `course_name` varchar(100) NOT NULL,
            `duration` varchar(50) NOT NULL,
            `fee` decimal(10,2) NOT NULL,
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Add some default courses
        $courses = [
            ['Computer Applications', '3 Months', 5000.00],
            ['Web Development', '6 Months', 10000.00],
            ['Graphic Design', '4 Months', 8000.00],
            ['Digital Marketing', '3 Months', 7000.00]
        ];

        $sql = "INSERT INTO `courses` (`course_name`, `duration`, `fee`) VALUES (?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);

        foreach ($courses as $course) {
            mysqli_stmt_bind_param($stmt, "ssd", $course[0], $course[1], $course[2]);
            mysqli_stmt_execute($stmt);
        }
    }

    // Check if fees table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'fees'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create fees table
        $sql = "CREATE TABLE IF NOT EXISTS `fees` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('paid','pending') NOT NULL DEFAULT 'pending',
            `payment_date` date DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `student_id` (`student_id`),
            KEY `course_id` (`course_id`),
            CONSTRAINT `fees_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
            CONSTRAINT `fees_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);
    } else {
        // Check if course_id column exists in fees table
        $column_exists = mysqli_query($conn, "SHOW COLUMNS FROM `fees` LIKE 'course_id'");

        if (mysqli_num_rows($column_exists) == 0) {
            // Add course_id column to fees table (without foreign key constraint initially)
            $sql = "ALTER TABLE `fees` ADD COLUMN `course_id` int(11) NOT NULL DEFAULT 1 AFTER `student_id`";
            mysqli_query($conn, $sql);

            // Update existing fee records with course_id from students table
            $sql = "UPDATE `fees` f JOIN `students` s ON f.student_id = s.id SET f.course_id = s.course_id";
            mysqli_query($conn, $sql);

            // Now add the index and foreign key constraint
            $sql = "ALTER TABLE `fees` ADD KEY `course_id` (`course_id`)";
            mysqli_query($conn, $sql);

            try {
                $sql = "ALTER TABLE `fees` ADD CONSTRAINT `fees_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE";
                mysqli_query($conn, $sql);
            } catch (Exception $e) {
                // If foreign key constraint fails, continue without it
                error_log("Failed to add foreign key constraint to fees table: " . $e->getMessage());
            }
        }
    }

    // Check if certificates table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'certificates'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create certificates table
        $sql = "CREATE TABLE IF NOT EXISTS `certificates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL,
            `certificate_number` varchar(50) NOT NULL,
            `issue_date` date NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `certificate_number` (`certificate_number`),
            KEY `student_id` (`student_id`),
            KEY `course_id` (`course_id`),
            CONSTRAINT `certificates_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
            CONSTRAINT `certificates_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);
    } else {
        // Check if course_id column exists in certificates table
        $column_exists = mysqli_query($conn, "SHOW COLUMNS FROM `certificates` LIKE 'course_id'");

        if (mysqli_num_rows($column_exists) == 0) {
            // Add course_id column to certificates table (without foreign key constraint initially)
            $sql = "ALTER TABLE `certificates` ADD COLUMN `course_id` int(11) NOT NULL DEFAULT 1 AFTER `student_id`";
            mysqli_query($conn, $sql);

            // Update existing certificate records with course_id from students table
            $sql = "UPDATE `certificates` c JOIN `students` s ON c.student_id = s.id SET c.course_id = s.course_id";
            mysqli_query($conn, $sql);

            // Now add the index and foreign key constraint
            $sql = "ALTER TABLE `certificates` ADD KEY `course_id` (`course_id`)";
            mysqli_query($conn, $sql);

            try {
                $sql = "ALTER TABLE `certificates` ADD CONSTRAINT `certificates_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE";
                mysqli_query($conn, $sql);
            } catch (Exception $e) {
                // If foreign key constraint fails, continue without it
                error_log("Failed to add foreign key constraint to certificates table: " . $e->getMessage());
            }
        }
    }

    // Check if student_courses table exists
    $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'student_courses'");

    if (mysqli_num_rows($table_exists) == 0) {
        // Create student_courses table
        $sql = "CREATE TABLE IF NOT EXISTS `student_courses` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `course_id` int(11) NOT NULL,
            `enrollment_date` date NOT NULL,
            `status` enum('ongoing','completed') NOT NULL DEFAULT 'ongoing',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `student_id` (`student_id`),
            KEY `course_id` (`course_id`),
            CONSTRAINT `student_courses_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
            CONSTRAINT `student_courses_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        mysqli_query($conn, $sql);

        // Migrate existing students to student_courses table
        $sql = "INSERT INTO `student_courses` (student_id, course_id, enrollment_date, status)
                SELECT id, course_id, registration_date, course_status FROM `students`
                WHERE NOT EXISTS (
                    SELECT 1 FROM `student_courses`
                    WHERE `student_courses`.student_id = `students`.id
                    AND `student_courses`.course_id = `students`.course_id
                )";

        mysqli_query($conn, $sql);
    }
}

// Initialize database
initializeDatabase();
?>
