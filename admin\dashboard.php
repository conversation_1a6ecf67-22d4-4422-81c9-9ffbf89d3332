<?php
$page_title = "Admin Dashboard";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['message'] = 'You must login as admin to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'admin/index.php');
}

$conn = getDbConnection();

// Get dashboard statistics
$total_students = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM students"))['count'];
$pending_students = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM students WHERE status = 'pending'"))['count'];
$total_courses = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM courses"))['count'];
$total_certificates = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM certificates"))['count'];
$total_enrollments = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM student_courses"))['count'];

// Get recent students with their primary course
$recent_students = mysqli_query($conn, "SELECT s.*, c.course_name
                                       FROM students s
                                       LEFT JOIN courses c ON s.course_id = c.id
                                       ORDER BY s.created_at DESC LIMIT 5");

// Get recent enrollments
$recent_enrollments = mysqli_query($conn, "SELECT sc.*, s.full_name, c.course_name, c.fee
                                         FROM student_courses sc
                                         JOIN students s ON sc.student_id = s.id
                                         JOIN courses c ON sc.course_id = c.id
                                         ORDER BY sc.created_at DESC LIMIT 5");

// Get recent certificates
$recent_certificates = mysqli_query($conn, "SELECT c.*, s.full_name, co.course_name
                                          FROM certificates c
                                          JOIN students s ON c.student_id = s.id
                                          JOIN courses co ON c.course_id = co.id
                                          ORDER BY c.created_at DESC LIMIT 5");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
        .dashboard-stats .card {
            border-left: 4px solid #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Admin Panel</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i> Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave"></i> Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i> Certificates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="posts.php">
                                <i class="fas fa-newspaper"></i> Posts/News
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-home"></i> Visit Website
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Dashboard Stats -->
                <div class="row dashboard-stats mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-muted">Total Students</h5>
                                <p class="card-text fs-4 fw-bold"><?php echo $total_students; ?></p>
                                <a href="students.php" class="text-decoration-none">View all <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-muted">Pending Approvals</h5>
                                <p class="card-text fs-4 fw-bold"><?php echo $pending_students; ?></p>
                                <a href="students.php?status=pending" class="text-decoration-none">View pending <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-muted">Total Courses</h5>
                                <p class="card-text fs-4 fw-bold"><?php echo $total_courses; ?></p>
                                <a href="courses.php" class="text-decoration-none">View all <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row dashboard-stats mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-muted">Total Course Enrollments</h5>
                                <p class="card-text fs-4 fw-bold"><?php echo $total_enrollments; ?></p>
                                <a href="student-enrollments.php" class="text-decoration-none">Manage enrollments <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-muted">Certificates Issued</h5>
                                <p class="card-text fs-4 fw-bold"><?php echo $total_certificates; ?></p>
                                <a href="certificates.php" class="text-decoration-none">View all certificates <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Recent Students -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recent Students</h5>
                            </div>
                            <div class="card-body">
                                <?php if (mysqli_num_rows($recent_students) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Course</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($student = mysqli_fetch_assoc($recent_students)): ?>
                                                    <tr>
                                                        <td><?php echo $student['full_name']; ?></td>
                                                        <td><?php echo $student['course_name']; ?></td>
                                                        <td>
                                                            <?php if ($student['status'] == 'pending'): ?>
                                                                <span class="badge bg-warning">Pending</span>
                                                            <?php elseif ($student['status'] == 'approved'): ?>
                                                                <span class="badge bg-success">Approved</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Rejected</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No students found.</p>
                                <?php endif; ?>
                                <div class="text-end">
                                    <a href="students.php" class="btn btn-sm btn-outline-primary">View All Students</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Course Enrollments -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recent Course Enrollments</h5>
                            </div>
                            <div class="card-body">
                                <?php if (mysqli_num_rows($recent_enrollments) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Student</th>
                                                    <th>Course</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($enrollment = mysqli_fetch_assoc($recent_enrollments)): ?>
                                                    <tr>
                                                        <td><?php echo $enrollment['full_name']; ?></td>
                                                        <td><?php echo $enrollment['course_name']; ?></td>
                                                        <td>
                                                            <?php if ($enrollment['status'] == 'ongoing'): ?>
                                                                <span class="badge bg-primary">Ongoing</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-success">Completed</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="student-courses.php?id=<?php echo $enrollment['student_id']; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No course enrollments found.</p>
                                <?php endif; ?>
                                <div class="text-end">
                                    <a href="students.php" class="btn btn-sm btn-outline-primary">View All Students</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Recent Certificates -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recent Certificates</h5>
                            </div>
                            <div class="card-body">
                                <?php if (mysqli_num_rows($recent_certificates) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Certificate #</th>
                                                    <th>Student</th>
                                                    <th>Course</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($certificate = mysqli_fetch_assoc($recent_certificates)): ?>
                                                    <tr>
                                                        <td><?php echo $certificate['certificate_number']; ?></td>
                                                        <td><?php echo $certificate['full_name']; ?></td>
                                                        <td><?php echo $certificate['course_name']; ?></td>
                                                        <td><?php echo formatDate($certificate['issue_date']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No certificates issued yet.</p>
                                <?php endif; ?>
                                <div class="text-end">
                                    <a href="certificates.php" class="btn btn-sm btn-outline-primary">View All Certificates</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="students.php?status=pending" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-user-check mb-2 d-block fs-4"></i>
                                    Approve Students
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="courses.php?action=add" class="btn btn-outline-success w-100 py-3">
                                    <i class="fas fa-plus-circle mb-2 d-block fs-4"></i>
                                    Add New Course
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="fees.php?status=pending" class="btn btn-outline-warning w-100 py-3">
                                    <i class="fas fa-money-check-alt mb-2 d-block fs-4"></i>
                                    Mark Fees Paid
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="posts.php?action=add" class="btn btn-outline-info w-100 py-3">
                                    <i class="fas fa-edit mb-2 d-block fs-4"></i>
                                    Add New Post
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="student-enrollments.php" class="btn btn-outline-secondary w-100 py-3">
                                    <i class="fas fa-book mb-2 d-block fs-4"></i>
                                    Manage Course Enrollments
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Modal Fix JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/modal-fix.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>
</body>
</html>
