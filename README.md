# City Technical Institute Marot Website

A complete website for City Technical Institute Marot with student registration, admin panel, certificate generation, and fee management features.

## Features

- Homepage with institute information and latest news
- Student registration system
- Admin panel for managing students, courses, fees, and certificates
- Certificate generation and verification
- Fee management
- Responsive design using Bootstrap 5

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- XAMPP/WAMP/LAMP stack (recommended)

## Installation

1. Clone or download this repository to your web server's document root (e.g., `htdocs` for XAMPP)
2. Create a MySQL database named `cti_db` (or update the database name in `includes/config.php`)
3. Update database credentials in `includes/config.php` if needed
4. Access `http://localhost/cti/init.php` to initialize the database and tables
5. Access the website at `http://localhost/cti/`

## Default Admin Login

- Username: admin
- Password: admin123

## Directory Structure

- `admin/` - Admin panel files
- `student/` - Student panel files
- `assets/` - CSS, JavaScript, images, and uploads
- `includes/` - Configuration and common functions

## License

This project is licensed under the MIT License.

## Credits

Developed for City Technical Institute Marot.
