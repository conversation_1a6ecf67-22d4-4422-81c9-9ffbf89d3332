<?php
/**
 * Placeholder Image Fixer Script
 * 
 * This script automatically replaces all external placeholder image URLs with local placeholder URLs
 * Run this script once to fix all placeholder image references in the codebase
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set header
header('Content-Type: text/plain');

echo "CTI Placeholder Image Fixer\n";
echo "==========================\n\n";

// Define the directories to scan
$directories = [
    __DIR__,
    __DIR__ . '/admin',
    __DIR__ . '/student',
    __DIR__ . '/includes'
];

// Define patterns to search for
$patterns = [
    'https://via.placeholder.com/(\d+)x(\d+)(\?text=([^"\'\s&]+))?',
    'https://placehold.it/(\d+)x(\d+)(\?text=([^"\'\s&]+))?',
    'https://placeholder.pics/(\d+)x(\d+)(\?text=([^"\'\s&]+))?'
];

// Track statistics
$stats = [
    'files_scanned' => 0,
    'files_modified' => 0,
    'replacements' => 0
];

// Function to process file
function processFile($file) {
    global $stats, $patterns;
    
    // Skip if not a PHP or HTML file
    if (!preg_match('/\.(php|html|htm)$/i', $file)) {
        return;
    }
    
    // Read file content
    $content = file_get_contents($file);
    $original_content = $content;
    $stats['files_scanned']++;
    
    // Process each pattern
    foreach ($patterns as $pattern) {
        $content = preg_replace_callback(
            '/' . $pattern . '/i',
            function($matches) {
                global $stats;
                $stats['replacements']++;
                
                $width = $matches[1];
                $height = $matches[2];
                $text = isset($matches[4]) ? $matches[4] : 'CTI+Marot';
                
                // Replace with our local placeholder function
                return '<?php echo getPlaceholderImage(' . $width . ', ' . $height . ', \'' . $text . '\'); ?>';
            },
            $content
        );
    }
    
    // If content was modified, save the file
    if ($content !== $original_content) {
        file_put_contents($file, $content);
        $stats['files_modified']++;
        echo "Modified: " . str_replace(__DIR__, '', $file) . "\n";
    }
}

// Process each directory
foreach ($directories as $directory) {
    echo "Scanning directory: " . str_replace(__DIR__, '', $directory) . "\n";
    
    // Get all PHP and HTML files
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory)
    );
    
    foreach ($files as $file) {
        if ($file->isFile()) {
            processFile($file->getPathname());
        }
    }
}

// Create default placeholder images
echo "\nCreating default placeholder images...\n";

// Define common placeholder sizes
$placeholders = [
    ['width' => 300, 'height' => 300, 'text' => 'CTI Marot'],
    ['width' => 400, 'height' => 250, 'text' => 'Course Image'],
    ['width' => 100, 'height' => 100, 'text' => 'Student'],
    ['width' => 200, 'height' => 200, 'text' => 'Profile']
];

// Output usage examples
echo "\nPlaceholder Usage Examples:\n";
echo "==========================\n";
foreach ($placeholders as $p) {
    $url = getPlaceholderImage($p['width'], $p['height'], $p['text']);
    echo "<img src=\"{$url}\" alt=\"{$p['text']}\">\n";
}

// Output statistics
echo "\nSummary:\n";
echo "Files scanned: {$stats['files_scanned']}\n";
echo "Files modified: {$stats['files_modified']}\n";
echo "Replacements made: {$stats['replacements']}\n";

echo "\nPlaceholder fix complete!\n";
?>
