<?php
/**
 * Simple placeholder image generator
 * This script generates basic placeholder images with text
 */

// Check if GD extension is available
if (!extension_loaded('gd')) {
    header('Content-Type: text/html');
    die('Error: GD extension is not available. Please enable it in your PHP configuration.');
}

// Set content type to image
header('Content-Type: image/png');

// Get parameters
$width = isset($_GET['width']) ? intval($_GET['width']) : 300;
$height = isset($_GET['height']) ? intval($_GET['height']) : 300;
$text = isset($_GET['text']) ? $_GET['text'] : 'CTI Marot';

// Limit dimensions for security
$width = min(max($width, 16), 1200);
$height = min(max($height, 16), 1200);

try {
    // Create image
    $image = @imagecreatetruecolor($width, $height);
    
    if (!$image) {
        throw new Exception('Failed to create image');
    }
    
    // Define colors
    $bg_color = imagecolorallocate($image, 238, 238, 238); // Light gray
    $text_color = imagecolorallocate($image, 51, 51, 51);  // Dark gray
    $border_color = imagecolorallocate($image, 200, 200, 200); // Medium gray
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Add border
    imagerectangle($image, 0, 0, $width - 1, $height - 1, $border_color);
    
    // Prepare text
    $text = urldecode($text);
    $text = str_replace('+', ' ', $text);
    
    // Use built-in font (5 is the largest built-in font)
    $font = 5;
    
    // Calculate text width and position
    $text_width = imagefontwidth($font) * strlen($text);
    $text_height = imagefontheight($font);
    
    // If text is too long, truncate it
    if ($text_width > $width * 0.8) {
        $max_chars = floor(($width * 0.8) / imagefontwidth($font));
        $text = substr($text, 0, $max_chars - 3) . '...';
        $text_width = imagefontwidth($font) * strlen($text);
    }
    
    // Calculate text position
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    // Draw text
    imagestring($image, $font, $x, $y, $text, $text_color);
    
    // Draw dimensions text
    $dim_text = $width . 'x' . $height;
    $dim_width = imagefontwidth(2) * strlen($dim_text);
    imagestring($image, 2, ($width - $dim_width) / 2, $y + $text_height + 10, $dim_text, $text_color);
    
    // Output image
    imagepng($image);
    imagedestroy($image);
    
} catch (Exception $e) {
    // Create a simple fallback image
    $image = @imagecreatetruecolor(300, 150);
    $bg = imagecolorallocate($image, 255, 255, 255);
    $text_color = imagecolorallocate($image, 0, 0, 0);
    
    imagefill($image, 0, 0, $bg);
    imagestring($image, 5, 10, 60, 'Placeholder Image Error', $text_color);
    imagestring($image, 3, 10, 80, $e->getMessage(), $text_color);
    
    imagepng($image);
    imagedestroy($image);
}
?>
