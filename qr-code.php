<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if certificate number is provided
if (!isset($_GET['cert'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('Certificate number is required');
}

$certificate_number = clean($_GET['cert']);

// Generate verification URL
$verification_url = SITE_URL . 'verify.php?cert=' . $certificate_number;

// Set content type to PNG image
header('Content-Type: image/png');

// Create QR code image
$size = 200; // Size of QR code
$margin = 2;   // Margin

// Create image with GD library
$image = imagecreatetruecolor($size, $size);
$white = imagecolorallocate($image, 255, 255, 255);
$blue = imagecolorallocate($image, 67, 97, 238); // #4361ee
imagefill($image, 0, 0, $white);

// Simple QR code pattern (just for demonstration)
// In a real implementation, you would use a proper QR code generation algorithm
$blockSize = floor(($size - 2 * $margin) / 21); // QR code is 21x21 modules for version 1
$startX = $margin;
$startY = $margin;

// Draw finder patterns (the three large squares in corners)
// Top-left finder pattern
imagefilledrectangle($image, $startX, $startY, $startX + 7 * $blockSize, $startY + 7 * $blockSize, $blue);
imagefilledrectangle($image, $startX + $blockSize, $startY + $blockSize, $startX + 6 * $blockSize, $startY + 6 * $blockSize, $white);
imagefilledrectangle($image, $startX + 2 * $blockSize, $startY + 2 * $blockSize, $startX + 5 * $blockSize, $startY + 5 * $blockSize, $blue);

// Top-right finder pattern
imagefilledrectangle($image, $startX + 14 * $blockSize, $startY, $startX + 21 * $blockSize, $startY + 7 * $blockSize, $blue);
imagefilledrectangle($image, $startX + 15 * $blockSize, $startY + $blockSize, $startX + 20 * $blockSize, $startY + 6 * $blockSize, $white);
imagefilledrectangle($image, $startX + 16 * $blockSize, $startY + 2 * $blockSize, $startX + 19 * $blockSize, $startY + 5 * $blockSize, $blue);

// Bottom-left finder pattern
imagefilledrectangle($image, $startX, $startY + 14 * $blockSize, $startX + 7 * $blockSize, $startY + 21 * $blockSize, $blue);
imagefilledrectangle($image, $startX + $blockSize, $startY + 15 * $blockSize, $startX + 6 * $blockSize, $startY + 20 * $blockSize, $white);
imagefilledrectangle($image, $startX + 2 * $blockSize, $startY + 16 * $blockSize, $startX + 5 * $blockSize, $startY + 19 * $blockSize, $blue);

// Draw some random data modules to make it look like a QR code
// This is just for visual effect, not a real QR code
srand(crc32($verification_url)); // Seed with the URL to get consistent pattern
for ($i = 0; $i < 100; $i++) {
    $x = rand(0, 20);
    $y = rand(0, 20);
    
    // Skip if in finder pattern areas
    if (($x < 8 && $y < 8) || ($x > 13 && $y < 8) || ($x < 8 && $y > 13)) {
        continue;
    }
    
    imagefilledrectangle(
        $image, 
        $startX + $x * $blockSize, 
        $startY + $y * $blockSize, 
        $startX + ($x + 1) * $blockSize - 1, 
        $startY + ($y + 1) * $blockSize - 1, 
        $blue
    );
}

// Add text to indicate it's a placeholder
$text_color = imagecolorallocate($image, 100, 100, 100);
imagestring($image, 2, $size/2 - 45, $size/2 - 5, "Scan to verify", $text_color);

// Output the image
imagepng($image);
imagedestroy($image);
?>
