<?php
$page_title = "Enroll in New Course";
require_once '../includes/config.php';
require_once '../includes/functions.php';

session_start();

// Check if user is logged in and is student
if (!isLoggedIn() || !isStudent()) {
    $_SESSION['message'] = 'You must login as student to access this page';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/index.php');
}

// Get student details
$student = getStudentByUserId($_SESSION['user_id']);

if (!$student) {
    $_SESSION['message'] = 'Student record not found';
    $_SESSION['message_type'] = 'danger';
    redirect(SITE_URL . 'student/logout.php');
}

$conn = getDbConnection();

// Get all active courses
$courses_query = "SELECT * FROM courses WHERE status = 'active' ORDER BY course_name ASC";
$courses_result = mysqli_query($conn, $courses_query);

// Get student's current courses
$enrolled_courses_query = "SELECT c.id, c.course_name, sc.status, sc.enrollment_date 
                          FROM student_courses sc 
                          JOIN courses c ON sc.course_id = c.id 
                          WHERE sc.student_id = ?";
$stmt = mysqli_prepare($conn, $enrolled_courses_query);
mysqli_stmt_bind_param($stmt, "i", $student['id']);
mysqli_stmt_execute($stmt);
$enrolled_courses_result = mysqli_stmt_get_result($stmt);

$enrolled_course_ids = [];
while ($enrolled_course = mysqli_fetch_assoc($enrolled_courses_result)) {
    $enrolled_course_ids[] = $enrolled_course['id'];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['enroll'])) {
    $course_id = (int) $_POST['course_id'];
    
    // Check if course exists
    $course_check_query = "SELECT * FROM courses WHERE id = ? AND status = 'active'";
    $stmt = mysqli_prepare($conn, $course_check_query);
    mysqli_stmt_bind_param($stmt, "i", $course_id);
    mysqli_stmt_execute($stmt);
    $course_result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($course_result) == 0) {
        $_SESSION['message'] = 'Invalid course selected';
        $_SESSION['message_type'] = 'danger';
        redirect(SITE_URL . 'student/enroll-course.php');
    }
    
    $course = mysqli_fetch_assoc($course_result);
    
    // Check if student is already enrolled in this course
    $check_query = "SELECT * FROM student_courses WHERE student_id = ? AND course_id = ?";
    $stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($stmt, "ii", $student['id'], $course_id);
    mysqli_stmt_execute($stmt);
    $check_result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($check_result) > 0) {
        $_SESSION['message'] = 'You are already enrolled in this course';
        $_SESSION['message_type'] = 'warning';
        redirect(SITE_URL . 'student/enroll-course.php');
    }
    
    // Enroll student in the course
    $enrollment_date = date('Y-m-d');
    $status = 'ongoing';
    
    $enroll_query = "INSERT INTO student_courses (student_id, course_id, enrollment_date, status) VALUES (?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $enroll_query);
    mysqli_stmt_bind_param($stmt, "iiss", $student['id'], $course_id, $enrollment_date, $status);
    
    if (mysqli_stmt_execute($stmt)) {
        // Create fee record for the new course
        $fee_query = "INSERT INTO fees (student_id, course_id, amount, status, payment_date) VALUES (?, ?, ?, 'pending', NULL)";
        $stmt = mysqli_prepare($conn, $fee_query);
        mysqli_stmt_bind_param($stmt, "iid", $student['id'], $course_id, $course['fee']);
        mysqli_stmt_execute($stmt);
        
        $_SESSION['message'] = 'Successfully enrolled in ' . $course['course_name'] . ' course';
        $_SESSION['message_type'] = 'success';
        redirect(SITE_URL . 'student/dashboard.php');
    } else {
        $_SESSION['message'] = 'Error enrolling in course: ' . mysqli_error($conn);
        $_SESSION['message_type'] = 'danger';
        redirect(SITE_URL . 'student/enroll-course.php');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>assets/css/style.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        .content {
            padding: 20px;
        }
        .course-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .course-card.enrolled {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5><?php echo SITE_NAME; ?></h5>
                        <p class="text-muted">Student Portal</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="enroll-course.php">
                                <i class="fas fa-book"></i> Enroll in Course
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Enroll in New Course</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Course Selection -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Available Courses</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">Select a course from the list below to enroll. You can enroll in multiple courses.</p>
                        
                        <div class="row">
                            <?php if (mysqli_num_rows($courses_result) > 0): ?>
                                <?php while ($course = mysqli_fetch_assoc($courses_result)): ?>
                                    <div class="col-md-4 mb-4">
                                        <div class="card course-card <?php echo in_array($course['id'], $enrolled_course_ids) ? 'enrolled' : ''; ?>">
                                            <div class="card-header bg-light">
                                                <h5 class="mb-0"><?php echo $course['course_name']; ?></h5>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Duration:</strong> <?php echo $course['duration']; ?></p>
                                                <p><strong>Fee:</strong> Rs. <?php echo number_format($course['fee'], 2); ?></p>
                                                
                                                <?php if (in_array($course['id'], $enrolled_course_ids)): ?>
                                                    <button class="btn btn-secondary w-100" disabled>Already Enrolled</button>
                                                <?php else: ?>
                                                    <form method="post" action="">
                                                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                        <button type="submit" name="enroll" class="btn btn-primary w-100">Enroll Now</button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <p class="mb-0">No courses are available for enrollment at this time. Please check back later.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>assets/js/script.js"></script>
</body>
</html>
