<?php
$page_title = "Home";
require_once 'includes/header.php';

// Process student login
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    $cnic = clean($_POST['cnic']);
    $password = $_POST['password'];

    $conn = getDbConnection();

    // Get student user
    $sql = "SELECT u.id, u.username, u.password, u.role
            FROM users u
            JOIN students s ON u.id = s.user_id
            WHERE s.cnic = ?";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $cnic);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) == 1) {
        $user = mysqli_fetch_assoc($result);

        // Verify password
        if (password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];

            // Redirect to student dashboard
            redirect(SITE_URL . 'student/dashboard.php');
        } else {
            $_SESSION['message'] = 'Invalid CNIC or password';
            $_SESSION['message_type'] = 'danger';
        }
    } else {
        $_SESSION['message'] = 'Invalid CNIC or password';
        $_SESSION['message_type'] = 'danger';
    }
}

// Get recent posts
$recent_posts = getRecentPosts(3);

// Get all courses for homepage
$all_courses = getAllCoursesForHomepage();
?>

<!-- Hero Section -->
<section class="hero text-center">
    <div class="container">
        <div data-aos="fade-up">
            <h1>Welcome to City Technical Institute Marot</h1>
            <p class="lead">Providing quality technical education and professional training since 2010</p>
            <div class="mt-4">
                <a href="register.php" class="btn btn-primary btn-lg me-2">
                    <i class="fas fa-user-plus me-2"></i>Register Now
                </a>
                <a href="#about" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-info-circle me-2"></i>Learn More
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Courses List Section -->
<section id="courses-list" class="py-5 bg-white">
    <div class="container">
        <div class="section-title text-center mb-4">
            <h2>Our Courses</h2>
            <div class="title-line mx-auto"></div>
            <p class="mt-3">Explore our technical and professional courses</p>
        </div>

        <style>
            .courses-table-container {
                height: 300px;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .courses-table-container thead th {
                position: sticky;
                top: 0;
                background-color: #0d6efd;
                color: white;
                z-index: 1;
            }
            .courses-table-container table {
                margin-bottom: 0;
            }
            .courses-table-container::-webkit-scrollbar {
                width: 8px;
            }
            .courses-table-container::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 8px;
            }
            .courses-table-container::-webkit-scrollbar-thumb {
                background: #0d6efd;
                border-radius: 8px;
            }
            .courses-table-container::-webkit-scrollbar-thumb:hover {
                background: #0b5ed7;
            }
        </style>

        <div class="courses-table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Course Name</th>
                        <th>Duration</th>
                        <th>Fee (Rs.)</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($all_courses) > 0): ?>
                        <?php foreach ($all_courses as $course): ?>
                            <tr>
                                <td><strong><?php echo $course['course_name']; ?></strong></td>
                                <td><?php echo $course['duration']; ?></td>
                                <td><?php echo number_format($course['fee'], 2); ?></td>
                                <td>
                                    <a href="register.php?course=<?php echo $course['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-user-plus me-1"></i> Enroll Now
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center py-3">No courses available at the moment.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                <div class="feature-box text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-graduation-cap fa-3x text-primary"></i>
                    </div>
                    <h4>Quality Education</h4>
                    <p class="mb-0">Expert instructors and industry-relevant curriculum for comprehensive learning.</p>
                </div>
            </div>
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                <div class="feature-box text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-certificate fa-3x text-primary"></i>
                    </div>
                    <h4>Certified Courses</h4>
                    <p class="mb-0">Receive recognized certificates to boost your professional credentials.</p>
                </div>
            </div>
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                <div class="feature-box text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-briefcase fa-3x text-primary"></i>
                    </div>
                    <h4>Career Support</h4>
                    <p class="mb-0">Guidance and placement assistance to help you start your professional journey.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="row mt-5">
    <!-- Main Content -->
    <div class="col-lg-8">
        <!-- About Section -->
        <section id="about" class="mb-5" data-aos="fade-up">
            <div class="section-title mb-4">
                <h2>About Our Institute</h2>
                <div class="title-line"></div>
            </div>
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-4 mb-4 mb-md-0">
                            <img src="assets/images/about-img.jpg" alt="About CTI" class="img-fluid rounded" onerror="this.src='<?php echo SITE_URL; ?>placeholder.php?width=300&height=300&text=CTI+Marot'" loading="lazy">
                        </div>
                        <div class="col-md-8">
                            <p>City Technical Institute Marot is a premier technical education provider located in Marot, Punjab, Pakistan. We offer a wide range of technical courses designed to equip students with practical skills and knowledge required in today's job market.</p>
                            <p>Our institute is committed to providing high-quality education through experienced instructors, modern facilities, and industry-relevant curriculum. We focus on both theoretical knowledge and practical training to ensure our students are well-prepared for their professional careers.</p>
                            <p>With a track record of producing skilled professionals, City Technical Institute Marot has established itself as a trusted name in technical education in the region.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest News Section -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-title mb-4">
                <h2>Latest News & Announcements</h2>
                <div class="title-line"></div>
            </div>
            <?php if (count($recent_posts) > 0): ?>
                <div class="row">
                    <?php foreach ($recent_posts as $index => $post): ?>
                        <div class="col-md-12" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                            <div class="card border-0 shadow-sm mb-4 news-item">
                                <div class="card-body p-4">
                                    <h5 class="card-title"><?php echo $post['title']; ?></h5>
                                    <p class="news-date">
                                        <i class="far fa-calendar-alt me-1"></i> <?php echo formatDate($post['created_at']); ?>
                                    </p>
                                    <p class="card-text"><?php echo substr($post['content'], 0, 200); ?>...</p>
                                    <a href="#" class="btn btn-sm btn-outline-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">No news or announcements available at the moment.</div>
            <?php endif; ?>
        </section>

        <!-- Location Map -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-title mb-4">
                <h2>Our Location</h2>
                <div class="title-line"></div>
            </div>
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d27814.54530153242!2d72.35!3d29.2!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x393b99f1e30b7c15%3A0x7c5b7c6dbaba6462!2sNear+Aalishan+School+Allah+Ho+Town+Marot%2C+Punjab%2C+Pakistan!5e0!3m2!1sen!2s!4v1625000000000!5m2!1sen!2s" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Student Login Form -->
        <div class="card border-0 shadow-sm mb-4" data-aos="fade-left">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Student Login</h5>
            </div>
            <div class="card-body p-4">
                <form method="post" action="">
                    <div class="mb-3">
                        <label for="cnic" class="form-label">CNIC Number</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                            <input type="text" class="form-control cnic-input" id="cnic" name="cnic" placeholder="12345-1234567-1" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" name="login" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                    <div class="text-center mt-3">
                        <small>Don't have an account? <a href="register.php">Register Now</a></small>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="card border-0 shadow-sm mb-4" data-aos="fade-left" data-aos-delay="100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-link me-2"></i>Quick Links</h5>
            </div>
            <div class="card-body p-0">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item p-3">
                        <a href="register.php" class="d-flex align-items-center text-decoration-none">
                            <div class="icon-box me-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">New Student Registration</h6>
                                <small class="text-muted">Register for courses</small>
                            </div>
                            <i class="fas fa-chevron-right ms-auto"></i>
                        </a>
                    </li>
                    <li class="list-group-item p-3">
                        <a href="verify-certificate.php" class="d-flex align-items-center text-decoration-none">
                            <div class="icon-box me-3">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Verify Certificate</h6>
                                <small class="text-muted">Check certificate authenticity</small>
                            </div>
                            <i class="fas fa-chevron-right ms-auto"></i>
                        </a>
                    </li>
                    <li class="list-group-item p-3">
                        <a href="admin/index.php" class="d-flex align-items-center text-decoration-none">
                            <div class="icon-box me-3">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Admin Login</h6>
                                <small class="text-muted">Access admin panel</small>
                            </div>
                            <i class="fas fa-chevron-right ms-auto"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card border-0 shadow-sm" data-aos="fade-left" data-aos-delay="200">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-address-card me-2"></i>Contact Us</h5>
            </div>
            <div class="card-body p-4">
                <address>
                    <div class="d-flex mb-3">
                        <div class="contact-icon me-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Address</h6>
                            <p class="mb-0">Near Aalishan School Allah Ho Town Marot</p>
                        </div>
                    </div>
                    <div class="d-flex mb-3">
                        <div class="contact-icon me-3">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Phone</h6>
                            <p class="mb-0">0344-7436314, 0306-4083348</p>
                        </div>
                    </div>
                    <div class="d-flex mb-3">
                        <div class="contact-icon me-3">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Email</h6>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                    </div>
                    <div class="d-flex">
                        <div class="contact-icon me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Working Hours</h6>
                            <p class="mb-0">Monday - Saturday: 9:00 AM - 5:00 PM</p>
                        </div>
                    </div>
                </address>
            </div>
        </div>
    </div>
</div>

<!-- Courses Section -->
<section class="py-5 mt-3" data-aos="fade-up">
    <div class="container">
        <div class="section-title text-center mb-5">
            <h2>Our Popular Courses</h2>
            <div class="title-line mx-auto"></div>
            <p class="mt-3">Explore our most popular technical and professional courses</p>
        </div>

        <style>
            .courses-slider {
                position: relative;
                padding: 0 50px;
            }
            .courses-slider .slick-slide {
                margin: 0 15px;
            }
            .courses-slider .slick-list {
                margin: 0 -15px;
            }
            .courses-slider .slick-prev,
            .courses-slider .slick-next {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 40px;
                height: 40px;
                background: #0d6efd;
                border-radius: 50%;
                color: white;
                text-align: center;
                line-height: 40px;
                z-index: 1;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .courses-slider .slick-prev:hover,
            .courses-slider .slick-next:hover {
                background: #0b5ed7;
            }
            .courses-slider .slick-prev {
                left: 0;
            }
            .courses-slider .slick-next {
                right: 0;
            }
            .course-card {
                margin-bottom: 20px;
                height: 100%;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }
            .course-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            }
        </style>

        <div class="courses-slider" data-aos="fade-up">
            <?php
            $delay = 100;

            foreach ($all_courses as $index => $course):
                // Determine if course should have a badge
                $has_badge = ($index == 0 || $index % 3 == 0);
                $badge_text = ($index == 0) ? 'Popular' : 'New';
            ?>
                <div>
                    <div class="course-card">
                        <div class="course-img">
                            <img src="<?php echo SITE_URL; ?>placeholder.php?width=400&height=250&text=<?php echo urlencode($course['course_name']); ?>" alt="<?php echo $course['course_name']; ?>" class="img-fluid">
                            <?php if ($has_badge): ?>
                                <div class="course-badge"><?php echo $badge_text; ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="course-content">
                            <h5><?php echo $course['course_name']; ?></h5>
                            <div class="course-info">
                                <span><i class="far fa-clock me-1"></i> <?php echo $course['duration']; ?></span>
                                <span><i class="fas fa-money-bill-wave me-1"></i> Rs. <?php echo number_format($course['fee'], 0); ?></span>
                            </div>
                            <p><?php echo !empty($course['description']) ? substr($course['description'], 0, 100) . '...' : 'Learn professional skills with our comprehensive ' . $course['course_name'] . ' course.'; ?></p>
                            <a href="register.php?course=<?php echo $course['id']; ?>" class="btn btn-outline-primary w-100">Enroll Now</a>
                        </div>
                    </div>
                </div>
            <?php
                $delay += 100;
            endforeach;

            // If no courses, add placeholder
            if (count($all_courses) == 0):
            ?>
                <div>
                    <div class="course-card">
                        <div class="course-img">
                            <img src="<?php echo SITE_URL; ?>placeholder.php?width=400&height=250&text=Coming+Soon" alt="Coming Soon" class="img-fluid">
                            <div class="course-badge">Coming Soon</div>
                        </div>
                        <div class="course-content">
                            <h5>New Course Coming Soon</h5>
                            <div class="course-info">
                                <span><i class="far fa-clock me-1"></i> TBA</span>
                                <span><i class="fas fa-book me-1"></i> TBA</span>
                            </div>
                            <p>We're developing new courses to help you advance your career. Stay tuned for updates!</p>
                            <a href="register.php" class="btn btn-outline-secondary w-100 disabled">Coming Soon</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Optimized Slick Slider Implementation -->
        <!-- Preload slick styles in header instead of middle of page -->
        <script type="text/javascript" data-defer="true">
            // Lazy load Slick Carousel only when needed and visible
            document.addEventListener('DOMContentLoaded', function() {
                // Check if courses slider exists
                const coursesSlider = document.querySelector('.courses-slider');
                if (!coursesSlider) return;
                
                // Function to initialize slider
                function initSlider() {
                    // Load jQuery only if not already loaded
                    if (typeof jQuery === 'undefined') {
                        const jqueryScript = document.createElement('script');
                        jqueryScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js';
                        jqueryScript.onload = loadSlick;
                        document.body.appendChild(jqueryScript);
                    } else {
                        loadSlick();
                    }
                }
                
                // Function to load Slick after jQuery
                function loadSlick() {
                    // Load Slick CSS
                    if (!document.querySelector('link[href*="slick.min.css"]')) {
                        const slickCss = document.createElement('link');
                        slickCss.rel = 'stylesheet';
                        slickCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css';
                        document.head.appendChild(slickCss);
                        
                        const slickThemeCss = document.createElement('link');
                        slickThemeCss.rel = 'stylesheet';
                        slickThemeCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css';
                        document.head.appendChild(slickThemeCss);
                    }
                    
                    // Load Slick JS
                    if (typeof $.fn.slick === 'undefined') {
                        const slickScript = document.createElement('script');
                        slickScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js';
                        slickScript.onload = initializeSlick;
                        document.body.appendChild(slickScript);
                    } else {
                        initializeSlick();
                    }
                }
                
                // Initialize Slick carousel
                function initializeSlick() {
                    $(coursesSlider).slick({
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        autoplay: true,
                        autoplaySpeed: 3000,
                        dots: true,
                        arrows: true,
                        lazyLoad: 'ondemand',
                        prevArrow: '<div class="slick-prev"><i class="fas fa-chevron-left"></i></div>',
                        nextArrow: '<div class="slick-next"><i class="fas fa-chevron-right"></i></div>',
                        responsive: [
                            {
                                breakpoint: 992,
                                settings: {
                                    slidesToShow: 2
                                }
                            },
                            {
                                breakpoint: 576,
                                settings: {
                                    slidesToShow: 1
                                }
                            }
                        ]
                    });
                }
                
                // Use Intersection Observer to load slider only when visible
                if ('IntersectionObserver' in window) {
                    const observer = new IntersectionObserver(function(entries) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting) {
                                initSlider();
                                observer.unobserve(entry.target);
                            }
                        });
                    }, {rootMargin: '100px'});
                    
                    observer.observe(coursesSlider);
                } else {
                    // Fallback for browsers without IntersectionObserver
                    window.addEventListener('load', initSlider);
                }
            });
        </script>

        <div class="text-center mt-4">
            <a href="#courses-list" class="btn btn-primary">View All Courses</a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-light" data-aos="fade-up">
    <div class="container">
        <div class="section-title text-center mb-5">
            <h2>What Our Students Say</h2>
            <div class="title-line mx-auto"></div>
            <p class="mt-3">Hear from our successful graduates</p>
        </div>

        <div class="row g-4">
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                <div class="testimonial-card">
                    <div class="testimonial-img">
                        <img src="https://via.placeholder.com/100x100" alt="Student" class="img-fluid rounded-circle">
                    </div>
                    <div class="testimonial-content">
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"The web development course at CTI Marot was excellent. The instructors were knowledgeable and the hands-on training helped me secure a job right after graduation."</p>
                        <h5>Ahmed Khan</h5>
                        <p class="testimonial-info">Web Developer at Tech Solutions</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                <div class="testimonial-card">
                    <div class="testimonial-img">
                        <img src="https://via.placeholder.com/100x100" alt="Student" class="img-fluid rounded-circle">
                    </div>
                    <div class="testimonial-content">
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"I took the graphic design course and it completely transformed my career. The practical approach and industry-relevant curriculum made all the difference."</p>
                        <h5>Fatima Ali</h5>
                        <p class="testimonial-info">Freelance Graphic Designer</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                <div class="testimonial-card">
                    <div class="testimonial-img">
                        <img src="https://via.placeholder.com/100x100" alt="Student" class="img-fluid rounded-circle">
                    </div>
                    <div class="testimonial-content">
                        <div class="testimonial-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                        <p>"The digital marketing course provided me with all the skills I needed to start my own agency. The certificate from CTI Marot helped me gain clients' trust."</p>
                        <h5>Usman Malik</h5>
                        <p class="testimonial-info">Founder, Digital Growth Agency</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="cta-section py-5" data-aos="fade-up">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <h2 class="text-white mb-2">Ready to Start Your Technical Journey?</h2>
                <p class="text-white mb-0">Register today and take the first step towards a successful career!</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="register.php" class="btn btn-light btn-lg">Register Now</a>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
