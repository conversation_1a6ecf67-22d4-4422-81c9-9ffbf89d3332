# QR Code Setup for Certificate Verification

This document explains how to set up the QR code generation feature for certificate verification.

## Requirements

The QR code generation feature requires the Endroid QR Code library. Here's how to install it:

### Installing Endroid QR Code Library

1. Make sure you have Composer installed on your server. If not, you can download it from [getcomposer.org](https://getcomposer.org/download/).

2. Open a terminal/command prompt and navigate to the root directory of the website (where the `composer.json` file is located).

3. Run the following command to install the QR Code library:

```
composer install
```

4. This will create a `vendor` directory with all the required dependencies.

## How It Works

1. Each certificate has a unique certificate number.
2. The QR code contains a URL that points to `verify.php?cert=[certificate_number]`.
3. When someone scans the QR code, they are directed to the verification page.
4. The verification page checks if the certificate number is valid and displays the certificate if it is.

## Fallback Mechanism

If the QR code library is not available, the system will display a placeholder image instead of a QR code. This ensures that certificates can still be generated even if the library is not installed.

## Customization

You can customize the QR code appearance by modifying the `qr-generator.php` file:

- Change the size of the QR code by modifying the `setSize()` parameter
- Change the colors by modifying the `setForegroundColor()` and `setBackgroundColor()` parameters
- Change the margin by modifying the `setMargin()` parameter

## Note for Developers

The QR code generation code checks if the library is available before attempting to use it:

```php
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
} else {
    // Display placeholder image
}
```

This ensures the system won't crash if the library is not installed.
