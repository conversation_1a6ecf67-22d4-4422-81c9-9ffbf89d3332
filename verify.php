<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if certificate number is provided
if (!isset($_GET['cert']) || empty($_GET['cert'])) {
    $error = 'Certificate number is required';
    include 'verify-result.php';
    exit;
}

$certificate_number = clean($_GET['cert']);
$conn = getDbConnection();

// Get certificate details
$sql = "SELECT c.*, s.full_name, s.father_name, s.cnic, co.course_name, co.duration
        FROM certificates c
        JOIN students s ON c.student_id = s.id
        JOIN courses co ON c.course_id = co.id
        WHERE c.certificate_number = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $certificate_number);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $error = 'Certificate not found';
    include 'verify-result.php';
    exit;
}

$certificate = mysqli_fetch_assoc($result);
include 'verify-result.php';
?>
